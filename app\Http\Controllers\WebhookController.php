<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\WithdrawalRequest;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function payeeSubmitted(Request $request)
    {
        Log::info("Payment successful: " . json_encode($request->all()));
        $data = $request->json()->all();
        Log::info("Event Data: " . json_encode($data['eventData']));
        $withdrawalRequest = WithdrawalRequest::where('reference_code', $data['eventData']['refCode'])->first();
        if ($withdrawalRequest) {
            // $withdrawalRequest->submission_date = Carbon::parse($data['eventData']['submissionDate'])->toDateTimeString();
            $withdrawalRequest->submission_date = Carbon::parse($data['eventData']['submissionDate'])->setTimezone('America/Bogota')->toDateTimeString();
            $withdrawalRequest->currency = $data['eventData']['amountSubmittedCurrency'];
            $withdrawalRequest->amount = $data['eventData']['amountSubmitted'];
            $withdrawalRequest->payee_fees = $data['eventData']['payeeFees'];
            $withdrawalRequest->payer_exchange_rate = $data['eventData']['payerExchangeRate'];
            $withdrawalRequest->payment_method_type = $data['eventData']['paymentMethodType'];
            $withdrawalRequest->status = 4;
            $withdrawalRequest->save();
        } else {
            Log::error("Withdrawal request not found for reference code: " . $data['eventData']['refCode']);
        }
        return response()->json(['message' => 'Payment data processed successfully']);
    }
    // public function paymentDeferred(Request $request)
    // {
    //     Log::info("Payment deferred: " . json_encode($request->all()));
    //     $data = $request->json()->all();
    //     Log::info("Deferred Event Data: " . json_encode($data['eventData']));
    //     $withdrawalRequest = WithdrawalRequest::where('reference_code', $data['eventData']['refCode'])->first();
    //     if ($withdrawalRequest) {
    //         // Update withdrawal request with deferred status and reason
    //         $withdrawalRequest->status = 5; // Assuming 5 is your deferred status
    //         $withdrawalRequest->rejected_reason = $data['eventData']['deferredReason'] ?? 'Payment deferred by Tipalti';
    //         $withdrawalRequest->deferred_date = Carbon::now()->toDateTimeString();
    //         // Update other relevant fields if available
    //         if (isset($data['eventData']['submissionDate'])) {
    //             $withdrawalRequest->submission_date = Carbon::parse($data['eventData']['submissionDate'])->toDateTimeString();
    //         }
    //         if (isset($data['eventData']['amountSubmitted'])) {
    //             $withdrawalRequest->amount = $data['eventData']['amountSubmitted'];
    //         }
    //         if (isset($data['eventData']['amountSubmittedCurrency'])) {
    //             $withdrawalRequest->currency = $data['eventData']['amountSubmittedCurrency'];
    //         }
    //         $withdrawalRequest->save();
    //         Log::info("Withdrawal request marked as deferred for reference code: " . $data['eventData']['refCode']);
    //     } else {
    //         Log::error("Withdrawal request not found for deferred payment, reference code: " . $data['eventData']['refCode']);
    //     }

    //     return response()->json(['message' => 'Payment deferred data processed successfully']);
    // }
    public function paymentDeferred(Request $request)
    {
        Log::info("Payment deferred: " . json_encode($request->all()));
        $data = $request->json()->all();
        Log::info("Deferred Event Data: " . json_encode($data['eventData']));
        $withdrawalRequest = WithdrawalRequest::where('reference_code', $data['eventData']['refCode'])->first();
        if ($withdrawalRequest) {
            // Update withdrawal request with deferred status
            $withdrawalRequest->status = 5; // Assuming 5 is your deferred status
            // Handle multiple deferred reasons as JSON
            if (isset($data['eventData']['deferredReasons']) && is_array($data['eventData']['deferredReasons'])) {
                // Store the complete reasons array as JSON
                $withdrawalRequest->rejected_reason = json_encode($data['eventData']['deferredReasons']);
            } else {
                // Fallback for single reason or no specific reason
                $withdrawalRequest->rejected_reason = json_encode([
                    [
                        'reasonCode' => 'DEFERRED',
                        'reasonDescription' => $data['eventData']['deferredReason'] ?? 'Payment deferred by Tipalti'
                    ]
                ]);
            }
            // Set deferred date from payload or current time
            if (isset($data['eventData']['deferredDate'])) {
                $withdrawalRequest->submission_date = Carbon::parse($data['eventData']['deferredDate'])->toDateTimeString();
            } else {
                $withdrawalRequest->submission_date = Carbon::now()->toDateTimeString();
            }

            // Update other relevant fields if available
            if (isset($data['eventData']['amountSubmitted'])) {
                $withdrawalRequest->amount = $data['eventData']['amountSubmitted'];
            }
            if (isset($data['eventData']['amountSubmittedCurrency'])) {
                $withdrawalRequest->currency = $data['eventData']['amountSubmittedCurrency'];
            }
            $withdrawalRequest->save();
            Log::info("Withdrawal request marked as deferred for reference code: " . $data['eventData']['refCode']);
            Log::info("Deferred reasons: " . $withdrawalRequest->rejected_reason);
        } else {
            Log::error("Withdrawal request not found for deferred payment, reference code: " . $data['eventData']['refCode']);
        }

        return response()->json(['message' => 'Payment deferred data processed successfully']);
    }
    // Handler for Payment Cancelled/Error events
    public function paymentCancelled(Request $request)
    {
        Log::info("Payment cancelled/error: " . json_encode($request->all()));
        $data = $request->json()->all();
        Log::info("Cancelled Event Data: " . json_encode($data['eventData']));
        $withdrawalRequest = WithdrawalRequest::where('reference_code', $data['eventData']['refCode'])->first();
        if ($withdrawalRequest) {
            // Update withdrawal request with cancelled/error status and reason
            $withdrawalRequest->status = 7; // Assuming 6 is your cancelled/error status
            $withdrawalRequest->rejected_reason = $data['eventData']['cancellationReason'] ??
                $data['eventData']['errorMessage'] ??
                'Payment cancelled or failed';
            $withdrawalRequest->submission_date = Carbon::now()->toDateTimeString();
            // Update other relevant fields if available
            if (isset($data['eventData']['submissionDate'])) {
                $withdrawalRequest->submission_date = Carbon::parse($data['eventData']['submissionDate'])->toDateTimeString();
            }
            if (isset($data['eventData']['amountSubmitted'])) {
                $withdrawalRequest->amount = $data['eventData']['amountSubmitted'];
            }
            if (isset($data['eventData']['amountSubmittedCurrency'])) {
                $withdrawalRequest->currency = $data['eventData']['amountSubmittedCurrency'];
            }
            $withdrawalRequest->save();
            Log::info("Withdrawal request marked as cancelled for reference code: " . $data['eventData']['refCode']);
        } else {
            Log::error("Withdrawal request not found for cancelled payment, reference code: " . $data['eventData']['refCode']);
        }

        return response()->json(['message' => 'Payment cancellation data processed successfully']);
    }
    public function payeeDetailChanged(Request $request)
    {
        Log::info("Payee detail changed: " . json_encode($request->all()));
        $data = $request->json()->all();
        Log::info("Event Data: " . json_encode($data['eventData']));
        if (isset($data['eventData']) && isset($data['eventData']['payeeId'])) {
            $payeeId = $data['eventData']['payeeId'];
            $status = $data['eventData']['status'] ?? null;
            $isPayable = $data['eventData']['isPayable'] ?? false;
            $paymentMethodType = $data['eventData']['paymentMethodType'] ?? 'N/A';
            $user = User::where('ids', $payeeId)->first();
            if ($user) {
                $is_payable_value = 0;
                if ($status === 'Active' && $isPayable === true) {
                    $is_payable_value = 1;
                }
                $user->is_payable = $is_payable_value;
                $user->payment_method = $paymentMethodType;
                $user->save();
                Log::info("User {$user->id} is_payable updated to {$is_payable_value}");
            } else {
                Log::error("User not found for payeeId: {$payeeId}");
            }
        } else {
            Log::error("Required data missing in webhook payload");
        }
        return response()->json(['message' => 'Payee detail changed successfully']);
    }
}
