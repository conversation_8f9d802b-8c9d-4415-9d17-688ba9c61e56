@extends('layouts.master')
@push('css')
    <link href="{{ asset('plugins/components/bootstrap-daterangepicker/daterangepicker.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/css/select2.min.css" rel="stylesheet" />
    <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui/dist/fancybox.css" />
    <style>
        .dataTables_length,
        .dataTables_filter {
            display: none;
        }

        .rating-option-single .rating_star_wrapper label {float: right; color: #bebebe; margin-right: 15px;}

    </style>
@endpush
@section('content')

    <section class="review_index_main_sec">

        <div class="container-fluid">
            <!-- .row -->
            <div class="row">
                <div class="col-md-12">
                    <h2 class="box-title">
                        {{-- {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'All Reviews') }} --}}
                        {{ translate('dashboard_reviews.all_reviews') }}
                    </h2>
                    <div class="head reviews_index_topbar d-flex justify-content-between">
                        {{-- @can('add-' . str_slug('Review')) --}}
                        {{-- <a class="btn btn-success pull-right" href="{{ url('/review/review/create') }}"><i class="icon-plus"></i>
                                {{ trans('add') }}
                                {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}</a> --}}
                        {{-- @endcan --}}
                        <div class="head_bt rounded_btn">
                            <ul class="nav nav-pills">
                                <li id="all-tab">
                                    <a data-toggle="pill" data-target="#all" href="javascript:void(0);">{{ translate('dashboard_reviews.all_reviews') }}</a>
                                </li>
                                <li id="reported-tab">
                                    <a data-toggle="pill" data-target="#reported" href="javascript:void(0);">{{ translate('dashboard_reviews.reported') }}</a>
                                </li>
                            </ul>
                        </div>
                        <div class="nav_search main d-flex">
                            <div class="btn-group filter_btn">
                                <a href="javascript:void(0)"
                                    class="d-flex align-items-center justify-content-center dropdown-toggle btn_yellow review_filter_btn"
                                    data-toggle="modal" data-target="#review_filter_modal" id="filter-btn">{{ translate('dashboard_reviews.filter') }} <i
                                        class="fa fa-filter ms-1"></i></a>
                            </div>
                            <!-- Actual search box -->
                            <div class="form-group filter_search has-feedback has-search">
                                <form class="example" action="{{ route('review.index') }}" id="review-form-filter"
                                    style="width: 100%;">
                                    <input type="hidden" value="{{ $request_data->filter ?? '' }}" name="filter" hidden>
                                    <div class="search d-flex gap-1" style="width: 100%">
                                        <button type="submit"><i class="fa fa-search"></i></button>
                                        <div class="search_field_wrapper w-100">
                                            <input type="text" class="w-100" placeholder="{{ translate('dashboard_reviews.search') }}" id="search-data-input"
                                                name="search" value="{{ request()->search }}" />
                                        </div>
                                        <input type="text" class="input-daterange-datepicker" value="{{ request()->date }}"
                                            name="date" placeholder="{{ translate('dashboard_reviews.select_date_range') }}">
                                    </div>
                                    <a id="reset-review-btn" {{-- href="{{ route('review.index') }}"  --}} class="btn btn-reset mb-0"><i
                                            class="fas fa-redo yellow_icon"></i></a>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-content">
                <div class="tab-pane fade" id="all">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="white-box">
                                {{-- <div class="clearfix"></div>
                        <hr> --}}
                                <div id="review-wrapper">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    {{-- <th>#</th> --}}
                                                    <th>{{ translate('dashboard_reviews.booking_id') }}</th>
                                                    <th>{{ translate('dashboard_reviews.customer_name') }}</th>
                                                    <th>{{ translate('dashboard_reviews.listing_name') }}</th>
                                                    <th>{{ translate('dashboard_reviews.service_provider_name') }}</th>
                                                    <th>{{ translate('dashboard_reviews.rating') }}</th>
                                                    <th>{{ translate('dashboard_reviews.date') }}</th>
                                                    <th class="d-none"></th>
                                                    <th>{{ translate('dashboard_reviews.actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                            </tbody>
                                        </table>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <div class="pagination-info">
                                                {{ translate('dashboard_reviews.showing_entries', ['from' => $review->firstItem(), 'to' => $review->lastItem(), 'total' => $review->total()]) }}
                                            </div>
                                            <div class="pagination-wrapper"> {!! $review->appends(['search' => Request::get('search')])->render() !!} </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="reported">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="white-box">
                                {{-- <div class="clearfix"></div>
                        <hr> --}}
                                <div class="table-responsive">
                                    <table class="table" id="reportedReviewsTable">
                                        <thead>
                                            <tr>
                                                {{-- <th>#</th> --}}
                                                {{-- <th>{{ trans('user_name') }}</th> --}}
                                                <th>{{ translate('dashboard_reviews.type') }}</th>
                                                <th>{{ translate('dashboard_reviews.reported_by') }}</th>
                                                <th>{{ translate('dashboard_reviews.review_by') }}</th>
                                                <th>{{ translate('dashboard_reviews.listing_name') }}</th>
                                                <th>{{ translate('dashboard_reviews.reason') }}</th>
                                                <th>{{ translate('dashboard_reviews.status') }}</th>
                                                <th>{{ translate('dashboard_reviews.actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @php
                                                $review_reports = $review_reports->sortBy('status'); // Sort by status (0 first, 1 last)
                                            @endphp
                                            @forelse ($review_reports as $review_report)
                                                <tr>
                                                    {{-- <td>{{ $loop->iteration ?? $item->id }}</td> --}}
                                                    {{-- <td>{{ ucfirst($review_report->type ?? '-') }}</td> --}}
                                                    <td>{{ ucfirst($review_report->type == 'response' ? 'Provider Response' : ($review_report->type == 'review' ? 'Customer Review' : $review_report->type ?? '-')) }}
                                                    </td>
                                                    <td>
                                                        @if ($review_report->user->ids)
                                                            @if (($review_report->user->roles[0]->name ?? '') == 'user')
                                                                <a
                                                                    href="{{ route('admin_details', ['id' => $review_report->user->ids]) }}">
                                                                @elseif (($review_report->user->roles[0]->name ?? '') == 'service')
                                                                    <a
                                                                        href="{{ route('service_provider', $review_report->user->ids) }}">
                                                                    @elseif (($review_report->user->roles[0]->name ?? '') == 'customer')
                                                                        <a
                                                                            href="{{ route('customer_details', ['id' => $review_report->user->ids]) }}">
                                                            @endif
                                                            {{ ($review_report->user->first_name ?? '-') . ' ' . $review_report->user->last_name }}
                                                            </a>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if (($review_report->user->roles[0]->name ?? '') == 'user')
                                                            <a
                                                                href="{{ route('admin_details', ['id' => $review_report->review->user->ids]) }}">
                                                            @elseif (($review_report->user->roles[0]->name ?? '') == 'service')
                                                                <a
                                                                    href="{{ route('service_provider', $review_report->review->user->ids) }}">
                                                                @elseif (($review_report->user->roles[0]->name ?? '') == 'customer')
                                                                    <a
                                                                        href="{{ route('customer_details', ['id' => $review_report->review->user->ids]) }}">
                                                        @endif
                                                        {{ ($review_report->review->user->first_name ?? '-') . ' ' . ($review_report->review->user->last_name ?? '-') }}
                                                        </a>
                                                    </td>
                                                    <td><a
                                                            href="{{ $review_report->listing && $review_report->listing->ids && $review_report->listing->slug ? url(app()->getLocale().'/'.'detail', ['listing_id' => $review_report->listing->ids, 'slug' => $review_report->listing->slug]) : 'javascript:void(0)' }}">{{ $review_report->listing->name ?? '-' }}</a>
                                                    </td>
                                                    <td>{{ $review_report->subject ?? '-' }}</td>
                                                    <td>
                                                        @if ($review_report->status == 0)
                                                            <span class="badge badge-danger">Pending</span>
                                                        @else
                                                            <span class="badge badge-warning">Resolved</span>
                                                        @endif
                                                    </td>
                                                    <td class="form_btn ">
                                                        <div class="dropdown">
                                                            <button class=" dropdown-toggle" type="button"
                                                                id="dropdownMenuButton" data-toggle="dropdown"
                                                                aria-haspopup="true" aria-expanded="false">
                                                                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                            </button>
                                                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                                @can('view-' . str_slug('Review'))
                                                                    <a class="view_review_btn dropdown-item" href="#!" data-toggle="modal"
                                                                        data-target="#view_review_modal"
                                                                        data-lisitng-id="{{ $review_report->review->listing_id ?? '' }}"
                                                                        data-booking-id="{{ $review_report->review->booking_id ?? '' }}"
                                                                        data-review-id="{{ $review_report->review_id ?? '' }}"
                                                                        title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                                                            <i class="fa fa-eye" aria-hidden="true"></i>
                                                                            View Review
                                                                    </a>
                                                                    @if ($review_report->subject == 'Other')
                                                                        <a class="other_reason_btn" href="#" data-toggle="modal"
                                                                            data-target="#other_reason_modal"
                                                                            title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                                                            <button class="dropdown-item other-reason-btn"
                                                                                data-other-reason="{{ $review_report->other_reason ?? '' }}">
                                                                                <i class="fa fa-eye" aria-hidden="true"></i>
                                                                                {{ trans('view') }} Reason
                                                                            </button>
                                                                        </a>
                                                                    @endif
                                                                @endcan
                                                                @can('edit-' . str_slug('Review'))
                                                                    <a class="edit_review_btn dropdown-item" href="#!" data-toggle="modal"
                                                                        data-target="#edit_review_modal"
                                                                        data-review-id="{{ $review_report->review_id ?? '' }}"
                                                                        title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                                                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                                            Edit Review
                                                                    </a>
                                                                @endcan
                                                                {{-- @can('edit-' . str_slug('Review'))
                                                                    <a class="dropdown-item" href="{{ url('/review/review/' . $item->id . '/edit') }}"
                                                                        title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                                                            {{ trans('edit') }}
                                                                    </a>
                                                                @endcan --}}
                                                                @if (!auth()->user()->hasRole('service'))
                                                                    @if ($review_report->status == 0)
                                                                        <a class="dropdown-item"
                                                                            href="{{ route('report_review_resolve', $review_report->id) }}">
                                                                                <i class="fa fa-check" aria-hidden="true"></i>
                                                                                {{ trans('Resolve') }}
                                                                        </a>
                                                                    @endif
                                                                @endif
                                                                @can('delete-' . str_slug('Review'))
                                                                    <form method="POST"
                                                                        action="{{ route('report_review_delete', $review_report->id) }}"
                                                                        id="delete-form-{{ $review_report->id }}"
                                                                        accept-charset="UTF-8" style="display:inline">
                                                                        {{ method_field('DELETE') }}
                                                                        {{ csrf_field() }}
                                                                        <button type="submit" class="dropdown-item text-danger"
                                                                            title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Review') }}">
                                                                            <i class="fas fa-trash" style="color: red;"></i>
                                                                            {{ trans('delete') }} Report
                                                                        </button>
                                                                    </form>
                                                                @endcan
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7">
                                                        <p class="text-center"> {{ __('No Report Found') }}</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                    {{-- <div class="pagination-wrapper"> {!! $review_reports->appends(['search' => Request::get('search')])->render() !!} </div> --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </section>

    {{-- Modal --}}
    <div class="modal fade review_filter_modal" id="review_filter_modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <span class="close" data-dismiss="modal">&times;</span>
                    <h1 class="modal-title" id="">{{ translate('dashboard_reviews.filter_modal_title') }}</h1>
                    <form id="review-filter-form" action="{{ route('review.index') }}" method="GET">
                        @csrf
                        <div class="filter_fields_wrapper">
                            <div class="row">
                                @if (!auth()->user()->hasRole('service'))
                                    <div
                                        class=" @if (!auth()->user()->hasRole('service')) col-md-6 @else col-md-12 @endif col-sm-12 col-12">
                                        <div class="form-group">
                                            <label for="listings">{{ translate('dashboard_reviews.customers') }}</label>
                                            <div class="cust_select form_field_padding mb-3 border">
                                                <select class="form-control select2" name="customers[]" id="customers"
                                                    data-placeholder="{{ translate('dashboard_reviews.select_customers') }}" multiple>
                                                    @foreach ($customers as $customer)
                                                        @if (isset($customer->first_name, $customer->last_name))
                                                            <option value="{{ $customer->id }}"
                                                                @if (in_array($customer->id, $request_data['customers'] ?? [])) selected @endif>
                                                                {{ ucwords($customer->first_name . ' ' . $customer->last_name) }}
                                                                ({{ $customer->email }})
                                                            </option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-12 col-12">
                                        <div class="form-group">
                                            <label for="listings">{{ translate('dashboard_reviews.service_provider') }}</label>
                                            <div class="cust_select form_field_padding mb-3 border">
                                                <select class="form-control select2" name="service_providers[]"
                                                    id="service_providers" data-placeholder="{{ translate('dashboard_reviews.select_service_providers') }}"
                                                    multiple>
                                                    @foreach ($service_providers as $service_provider)
                                                        @if (isset($service_provider->first_name, $service_provider->last_name))
                                                            <option value="{{ $service_provider->id }}"
                                                                @if (in_array($service_provider->id, $request_data['service_providers'] ?? [])) selected @endif>
                                                                {{ ucwords($service_provider->first_name . ' ' . $service_provider->last_name) }}
                                                            </option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="row">
                                <div class="col-md-12 col-sm-12 col-12 listing_col">
                                    <div class="form-group">
                                        <label for="listings">{{-- translate('dashboard_reviews.listings') --}}</label>
                                        <div class="cust_select form_field_padding mb-3 border">
                                            <select class="form-control select2" name="listings[]" id="listings"
                                                data-placeholder="{{ translate('dashboard_reviews.select_listings') }}" multiple>
                                                @foreach ($listings as $listing)
                                                    @if (isset($listing->name))
                                                        <option value="{{ $listing->id ?? '-' }}"
                                                            @if (in_array($listing->id, $request_data['listings'] ?? [])) selected @endif>
                                                            {{ ucwords($listing->name) ?? '-' }}
                                                        </option>
                                                    @endif
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-6 col-6 status_col" style="display: none;">
                                    <div class="form-group">
                                        <label for="listings">{{-- translate('dashboard_reviews.status') --}}</label>
                                        <div class="cust_select form_field_padding mb-3 border">
                                            <select class="form-control select2_single" name="status" id="review_status"
                                                data-placeholder="{{ translate('dashboard_reviews.select_status') }}">
                                                <option value="all"
                                                    {{ ($request_data['status'] ?? '') === 'all' ? 'selected' : '' }}>{{ translate('dashboard_reviews.all') }}
                                                </option>
                                                <option value="0"
                                                    {{ ($request_data['status'] ?? '') == 0 ? 'selected' : '' }}>{{ translate('dashboard_reviews.pending') }}
                                                </option>
                                                <option value="1"
                                                    {{ ($request_data['status'] ?? '') == 1 ? 'selected' : '' }}>{{ translate('dashboard_reviews.resolved') }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal_btn_wrapper d-flex justify-content-center gap-2">
                            <button type="submit" class="btn create_btn">{{ translate('dashboard_reviews.apply') }}</button>
                            <a class="btn cancel_btn" href="{{ route('review.index') }}">{{ translate('dashboard_reviews.clear') }}</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {{-- view review modal --}}

    <div class="modal fade review" id="view_review_modal" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-all ">
            {{-- <form action="{{ route('review_post') }}" enctype="multipart/form-data" method="POST"> --}}
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        @csrf
                        <div class="modal-header justify-content-center">
                            <h3 class="modal-title text-center fs-20" id="exampleModalLabel">
                                {{ translate('dashboard_reviews.review_details') }}
                            </h3>
                            <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close"><i
                                    class="fas fa-times"></i></button>
                        </div>
                        <div class="modal-body mb-3 pt-0">
                        </div>
                    </div>
                </div>
            {{-- </form> --}}
        </div>
    </div>

    {{-- view review modal end --}}

    {{-- Edit review modal --}}

    <div class="modal fade review" id="edit_review_modal" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-all ">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header justify-content-center">
                        <h3 class="modal-title text-center fs-20" id="exampleModalLabel">
                            {{ translate('dashboard_reviews.edit_review') }}
                        </h3>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body mb-3 pt-0">
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Edit review modal end --}}
    <!-- Reprt Review Modal -->
    <div class="modal fade review-reoprt" id="report_review" tabindex="-1" aria-labelledby="exampleModalLabel">
        <div class="modal-all ">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content" id="report_review_form">
                    <form action="{{ route('report_review') }}" method="POST" class="report_review_form pe-3">

                        <h3 class="modal-title text-center fs-20">
                            {{ translate('dashboard_reviews.report_review') }}
                            <span class="close" data-dismiss="modal" aria-label="Close">
                                <i class="fas fa-times"></i>
                            </span>
                        </h3>

                        @csrf
                        <input type="hidden" name="review_id" id="review_id">
                        <input type="hidden" name="report_type" value="review">
                        <input type="hidden" value="{{ @$review->listing->ids ??''}}" name="listing_id">
                        <div class="modal-body pb-0">

                            <div class="reasons_wrapper">
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_1" value="{{ translate('dashboard_reviews.inappropriate_language') }}"
                                        checked>
                                    <label for="reason_1">{{ translate('dashboard_reviews.inappropriate_language') }}</label>
                                </div>
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_2"
                                        value="{{ translate('dashboard_reviews.discrimination') }}">
                                    <label for="reason_2">{{ translate('dashboard_reviews.discrimination') }}</label>
                                </div>
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_3"
                                        value="{{ translate('dashboard_reviews.harassment_bullying') }}">
                                    <label for="reason_3">{{ translate('dashboard_reviews.harassment_bullying') }}</label>
                                </div>
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_4" value="{{ translate('dashboard_reviews.violates_privacy') }}">
                                    <label for="reason_4">{{ translate('dashboard_reviews.violates_privacy') }}</label>
                                </div>
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_5"
                                        value="{{ translate('dashboard_reviews.spam_irrelevant') }}">
                                    <label for="reason_5">{{ translate('dashboard_reviews.spam_irrelevant') }}</label>
                                </div>
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_6"
                                        value="{{ translate('dashboard_reviews.false_misleading') }}">
                                    <label for="reason_6">{{ translate('dashboard_reviews.false_misleading') }}</label>
                                </div>
                                <div class="custom_radio">
                                    <input type="radio" name="subject" id="reason_7" value="{{ translate('dashboard_reviews.other') }}">
                                    <label for="reason_7">{{ translate('dashboard_reviews.other') }}</label>
                                </div>
                            </div>

                            <div class="mb-3 mt-2 form_field_padding">
                                <textarea name="other_reason" id="other_reason" class="form-control other_reason d-none" rows="5"
                                    placeholder="{{ translate('dashboard_reviews.provide_more_details') }}"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer border-0">
                            <button type="submit" class="btn_yellow btn button1 text-black w-100">{{ translate('dashboard_reviews.submit') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Reprt Review Modal End -->
    {{-- Edit other modal --}}
    <div class="modal fade review" id="other_reason_modal" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-all ">
            <div class="modal-dialog modal-md">
                <div class="modal-content">
                    <div class="modal-header justify-content-center">
                        <h3 class="modal-title text-center fs-20" id="exampleModalLabel">
                            {{ translate('dashboard_reviews.other_reason') }}
                        </h3>
                        <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body mb-3" id="other_modal_body">
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Edit other modal end --}}
@endsection
@push('js')
    <script src="{{ asset('plugins/components/bootstrap-daterangepicker/daterangepicker.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui/dist/fancybox.umd.js"></script>
    <script>
        $(document).ready(function() {
            $('#allReviewsTable').DataTable({
                order: [
                    [4, 'desc']
                ]
            });
        });
    </script>
    <script>
        $(document).on('click', '.report_review_btn', function() {
            const reviewId = $(this).attr('data-review-id');
            $('#review_id').val(reviewId);
        });
        $(document).ready(function() {
            const getReview = (data = null) => {
                $.ajax({
                    url: `{{ route('review_filter') }}`,
                    type: "GET",
                    data: data,
                    beforeSend: function() {
                        $('#review-wrapper table tbody').html(
                            '<tr><td colspan="7" class="text-center loading">Loading...</td></tr>'
                        );
                    },
                    success: function(response) {
                        // Check if response is an error object
                        if (typeof response === 'object' && response.error) {
                            $('#review-wrapper table tbody').html(
                                '<tr><td colspan="7" class="text-center text-danger">Error: ' +
                                response.message + '</td></tr>');
                        } else {
                            $('#review-wrapper').html(response);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        $('#review-wrapper table tbody').html(
                            '<tr><td colspan="7" class="text-center text-danger loading">Error loading reviews. Please try again.</td></tr>'
                        );
                    },
                    complete: function() {
                        // Prevent any form submission that might cause page reload
                        return false;
                    }
                });

                // Prevent default form submission that might cause page reload
                return false;
            }

            $("#review-filter-form").on("submit", function(e) {
                e.preventDefault();
                let formData = $(this).serializeArray();

                // Get the current active tab
                const currentHash = window.location.hash || '#all';

                // Add the tab information to the form data
                formData.push({
                    name: 'active_tab',
                    value: currentHash.replace('#', '')
                });

                // If we're on the reported tab, show the status field
                if (currentHash === '#reported') {
                    $(".status_col").show();
                    // Apply filter directly to the reported reviews table
                    filterReportedTable(formData);
                } else {
                    $(".status_col").hide();
                    // Use the existing AJAX call for the reviews tab
                    getReview(formData);
                }

                $("#review_filter_modal").modal("hide");
            })
            // testing
            $(document).on('click', '.pagination a', function(e) {
                e.preventDefault();
                let page = $(this).attr('href').split('page=')[1];
                let formData = $('#review-form-filter').serializeArray();
                formData.push({
                    name: 'page',
                    value: page
                });
                getReview(formData);
            });

            let debounceTimeout;
            $(document).on("keyup", "#search-data-input", function(event) {
                event.preventDefault();
                clearTimeout(debounceTimeout);
                debounceTimeout = setTimeout(function() {
                    let form = $("#review-form-filter");
                    let formData = form.serializeArray();
                    getReview(formData);
                }, 500);
            });

            // Reset button functionality
            $("#reset-review-btn").on("click", function(e) {
                e.preventDefault();

                // Clear all form fields
                $("#search-data-input").val('');
                $(".input-daterange-datepicker").val('');

                // Reset the review table to show all reviews
                getReview();

                return false;
            });

            $(document).on("submit", "#review-form-filter", function(e) {
                e.preventDefault(); // Use the event parameter
                let form = $(this);
                let formData = form.serializeArray();
                getReview(formData);
                return false; // Prevent form submission
            })
            $(document).on("click", "#reset-review-btn", function(event) {
                event.preventDefault();
                getReview();
            })
            getReview();

            function showTabByHash() {
                var currentHash = window.location.hash;

                // If no hash, default to #all
                if (!currentHash || currentHash === '') {
                    currentHash = '#all';
                    window.history.replaceState(null, null, window.location.href.split('#')[0] + '#all');
                }

                console.log('Current hash:', currentHash);

                // Remove active class from all tabs and tab panes
                $('.nav-pills li').removeClass('active');
                $('.tab-pane').removeClass('active show in fade');

                // Find the tab with matching data-target and activate it
                var $targetTab = $('.nav-pills a[data-target="' + currentHash + '"]');

                if ($targetTab.length > 0) {
                    // Add active class to the target tab and its content
                    $targetTab.closest('li').addClass('active');
                    $(currentHash).addClass('active show in fade');

                    console.log('Activated tab:', currentHash);
                } else {
                    // If no matching tab found, default to #all
                    console.log('No matching tab found, defaulting to #all');
                    $('.nav-pills a[data-target="#all"]').closest('li').addClass('active');
                    $('#all').addClass('active show in fade');
                    window.history.replaceState(null, null, window.location.href.split('#')[0] + '#all');
                    currentHash = '#all';
                }

                // Handle modal column layout based on active tab
                var tabName = currentHash.replace("#", "");
                if (tabName == 'all') {
                    $('#review_filter_modal .listing_col').removeClass('col-md-6 col-sm-6 col-6');
                    $('#review_filter_modal .listing_col').addClass('col-md-12 col-sm-12 col-12');
                    $('#review_filter_modal .status_col').hide();
                    $('#review_filter_modal .listing_col').show();
                } else {
                    $('#review_filter_modal .listing_col').removeClass('col-md-12 col-sm-12 col-12');
                    $('#review_filter_modal .listing_col').addClass('col-md-6 col-sm-6 col-6');
                    $('#review_filter_modal .status_col').show();
                }

                // Show/hide status field based on active tab
                if (currentHash === '#reported') {
                    $(".status_col").show();
                } else {
                    $(".status_col").hide();
                }

                $('html, body').animate({
                    scrollTop: 80
                }, 0);
            }
            // Initialize tabs on page load
            showTabByHash();

            // Handle hash changes (back/forward browser buttons)
            $(window).on('hashchange', showTabByHash);
            $('.nav-pills a').on('click', function(e) {
                e.preventDefault();
                var target = $(this).data('target');
                console.log("Clicked tab target:", target);

                // Update the URL with the data-target value
                var currentUrl = window.location.href.split('#')[0]; // Get current URL without hash
                var newUrl = currentUrl + target; // Append target (which already includes #)

                // Update the URL without page reload
                window.history.pushState(null, null, newUrl);

                // Show the tab
                showTabByHash();
            });
            $('form').on('submit', function(e) {
                var currentHash = window.location.hash || '#all';
                window.location.hash = currentHash;
                showTabByHash();
            });
        });
    </script>
    <script>
        $('.input-daterange-datepicker').daterangepicker({
            buttonClasses: ['btn', 'custom_cal_btn'],
            opens: 'left',
            applyClass: 'btn_yellow',
            cancelClass: 'cancel_btn',
            autoUpdateInput: false,
            locale: {
                format: 'MM/DD/YYYY'
            }
        });
        $('.input-daterange-datepicker').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
        });

        $('.input-daterange-datepicker').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
        });
        $(document).ready(function() {
            $(".select2").select2({
                closeOnSelect: false,
                placeholder: $(this).data('placeholder'),
                allowClear: false,
                multiple: true,
                dropdownParent: $('#review_filter_modal')
            });

            $(".select2_single").select2({
                closeOnSelect: true,
                placeholder: $(this).data('placeholder'),
                allowClear: false,
                multiple: false,
                dropdownParent: $('#review_filter_modal'),
                minimumResultsForSearch: Infinity
            });

            // Show/hide status field when filter modal is opened
            $("#filter-btn").on("click", function() {
                const currentHash = window.location.hash || '#all';
                if (currentHash === '#reported') {
                    $(".status_col").show();
                } else {
                    $(".status_col").hide();
                }
            });

            // Update listings when service provider changes
            $("#service_providers").on('change', function() {
                const selectedProviders = $(this).val();

                // Show loading indicator
                $("#listings").html('<option value="">Loading listings...</option>');

                // Fetch listings for selected providers
                $.ajax({
                    url: "{{ route('get_provider_listings') }}",
                    type: "GET",
                    data: {
                        provider_ids: selectedProviders
                    },
                    success: function(response) {
                        if (response.status) {
                            // Clear and update the listings dropdown
                            $("#listings").empty();

                            if (response.listings.length > 0) {
                                // Add listings to dropdown
                                $.each(response.listings, function(index, listing) {
                                    $("#listings").append(
                                        $('<option></option>')
                                        .attr('value', listing.id)
                                        .text(listing.name || "-")
                                    );
                                });
                            } else {
                                // No listings found
                                $("#listings").append(
                                    $('<option></option>')
                                    .attr('value', '')
                                    .text('No listings found for selected provider(s)')
                                );
                            }

                            // Refresh Select2
                            $("#listings").trigger('change');
                        }
                    },
                    error: function() {
                        // Error handling
                        $("#listings").html('<option value="">Error loading listings</option>');
                    }
                });
            });

            $(".create_btn").click(function() {
                // Get form data
                let formData = $("#review-filter-form").serializeArray();

                // Get the current active tab
                const currentHash = window.location.hash || '#all';

                // Add the tab information to the form data
                formData.push({
                    name: 'active_tab',
                    value: currentHash.replace('#', '')
                });

                // If we're on the reported tab, show the status field and use the reported filter
                if (currentHash === '#reported') {
                    $(".status_col").show();
                    filterReportedTable(formData);
                } else {
                    // For the all tab, use the existing filter function
                    let selectedCustomers = $("#customers").val() || [];
                    let selectedProviders = $("#service_providers").val() || [];
                    let selectedListings = $("#listings").val() || [];
                    filterTable("#allReviewsTable tbody tr", selectedCustomers, selectedProviders,
                        selectedListings);
                }

                $("#review_filter_modal").modal("hide");
            });

            // Function to filter the reported reviews table
            function filterReportedTable(formData) {
                // Extract filter values from formData
                let selectedCustomers = [];
                let selectedProviders = [];
                let selectedListings = [];
                let selectedStatus = 'all';

                // Parse the form data to get filter values
                formData.forEach(item => {
                    if (item.name === 'customers[]') {
                        selectedCustomers.push(item.value);
                    } else if (item.name === 'service_providers[]') {
                        selectedProviders.push(item.value);
                    } else if (item.name === 'listings[]') {
                        selectedListings.push(item.value);
                    } else if (item.name === 'status') {
                        selectedStatus = item.value;
                    }
                });

                console.log("Filtering reported reviews with:", {
                    customers: selectedCustomers,
                    providers: selectedProviders,
                    listings: selectedListings,
                    status: selectedStatus
                });

                // Apply filters to the reported reviews table
                $("#reportedReviewsTable tbody tr").each(function() {
                    // Skip empty rows or "No Review Found" messages
                    if ($(this).find("td").length <= 1) {
                        return true; // continue to next iteration
                    }

                    // Get values from table cells - adjust indices based on actual table structure
                    let reportedBy = $(this).find("td:nth-child(2)").text().trim();
                    let reviewBy = $(this).find("td:nth-child(3)").text().trim();
                    let listing = $(this).find("td:nth-child(4)").text().trim();
                    let status = $(this).find("td:nth-child(6)").text().trim();

                    // Check if status matches (if filtering by status)
                    let statusMatch = true;
                    if (selectedStatus !== 'all') {
                        if (selectedStatus === '0' && status.includes('Pending')) {
                            statusMatch = true;
                        } else if (selectedStatus === '1' && status.includes('Resolved')) {
                            statusMatch = true;
                        } else {
                            statusMatch = false;
                        }
                    }

                    // Check if customer filter matches
                    let customerMatch = true;
                    if (selectedCustomers.length > 0) {
                        customerMatch = false;
                        for (let i = 0; i < selectedCustomers.length; i++) {
                            let customerId = selectedCustomers[i];
                            // Look for customer ID in the links
                            if ($(this).find("td:nth-child(2) a").attr("href")?.includes(customerId) ||
                                $(this).find("td:nth-child(3) a").attr("href")?.includes(customerId)) {
                                customerMatch = true;
                                break;
                            }
                        }
                    }

                    // Check if provider filter matches
                    let providerMatch = true;
                    if (selectedProviders.length > 0) {
                        providerMatch = false;
                        for (let i = 0; i < selectedProviders.length; i++) {
                            let providerId = selectedProviders[i];
                            // Look for provider ID in the links
                            if ($(this).find("td:nth-child(2) a").attr("href")?.includes(providerId) ||
                                $(this).find("td:nth-child(3) a").attr("href")?.includes(providerId)) {
                                providerMatch = true;
                                break;
                            }
                        }
                    }

                    // Check if listing filter matches
                    let listingMatch = true;
                    if (selectedListings.length > 0) {
                        listingMatch = false;
                        for (let i = 0; i < selectedListings.length; i++) {
                            let listingId = selectedListings[i];
                            // Check if the listing ID is in the listing column
                            if ($(this).find("td:nth-child(4) a").attr("href")?.includes(listingId)) {
                                listingMatch = true;
                                break;
                            }
                        }
                    }

                    // Show/hide row based on filter matches
                    if (customerMatch && providerMatch && listingMatch && statusMatch) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            // Function to filter the reviews table
            function filterTable(tableSelector, customers, providers, listings) {
                $(tableSelector).each(function() {
                    let reviewBy = $(this).find("td:nth-child(6)").text().trim().replace(/\s+/g, ' ') || $(
                        this).find("td:nth-child(6)").html().trim().replace(/\s+/g, ' ');
                    let provider = $(this).find("td:nth-child(3)").text().trim().replace(/\s+/g, ' ');
                    let listing = $(this).find("td:nth-child(2)").text().trim().replace(/\s+/g, ' ');
                    let customerMatch = customers.length === 0 || customers.includes(reviewBy);
                    let providerMatch = providers.length === 0 || providers.includes(provider);
                    let listingMatch = listings.length === 0 || listings.includes(listing);
                    if (customerMatch && providerMatch && listingMatch) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }
            // Reset Filters
            $(".cancel_btn").click(function(e) {
                // Prevent default action (which might cause page reload)
                e.preventDefault();

                // Reset all form fields
                $("#customers, #service_providers, #listings, #review_status").val(null).trigger("change");

                // Get the current active tab
                const currentHash = window.location.hash || '#all';

                // Reset the appropriate table based on the active tab
                if (currentHash === '#reported') {
                    $("#reportedReviewsTable tbody tr").show();
                } else {
                    $("#allReviewsTable tbody tr").show();
                    // Use the existing getReview function to reset the reviews
                    getReview();
                }

                $("#review_filter_modal").modal("hide");
            });
            $("#allReviewsTable .delete_btn").click(function(e) {
                e.preventDefault();
                let form = $(this).closest("form");
                Swal.fire({
                    title: "Are you sure?",
                    text: "This action cannot be reverted",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Yes, delete it!",
                    cancelButtonText: "Cancel"
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
            let loader = `<div id="listing-loading"><div class="loader"></div></div>`;
            $(document).on("click", ".view_review_btn", function() {
                var view_review = true;
                let booking_id = $(this).attr("data-booking-id");
                let review_id = $(this).data("review-id")
                let reply_mode = $(this).data("reply-mode") || false;

                // Set modal title based on mode
                if (reply_mode) {
                    const heading_text_reply = `{{ translate('dashboard_reviews.reply_to_review') }}`;
                    $("#view_review_modal .modal-title").text(heading_text_reply);
                } else {
                    const heading_text = `{{ translate('dashboard_reviews.review_details') }}`;
                    $("#view_review_modal .modal-title").text(heading_text);
                }

                $("#view_review_modal .modal-body").html(loader);
                $.ajax({
                    url: "{{ route('view_review_details') }}",
                    type: "GET",
                    data: {
                        booking_id: booking_id,
                        review_id: review_id,
                        view_review: view_review,
                        reply_mode: reply_mode
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#view_review_modal .modal-body").html(response.data);
                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    }
                });
            });

            // Handle reply button click
            $(document).on("click", ".reply_review", function() {
                var view_review = true;
                let booking_id = $(this).attr("data-booking-id");
                let review_id = $(this).data("review-id");
                let reply_mode = true;

                // Set modal title for reply mode
                const heading_text_reply = `{{ translate('dashboard_reviews.reply_to_review') }}`;
                $("#view_review_modal .modal-title").text(heading_text_reply);

                $("#view_review_modal .modal-body").html(loader);
                $.ajax({
                    url: "{{ route('view_review_details') }}",
                    type: "GET",
                    data: {
                        booking_id: booking_id,
                        review_id: review_id,
                        view_review: view_review,
                        reply_mode: reply_mode
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#view_review_modal .modal-body").html(response.data);
                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    }
                });
            });

            // Handle reply form submission
            $(document).on("submit", ".reply_form", function(e) {
                e.preventDefault();
                let form = $(this);
                let textarea = form.find(".reply_textarea"); // target your textarea
                let replyText = textarea.val().trim();
                let submitBtn = form.find('button[type="submit"]');

                if (replyText === "") {
                    console.log("Empty reply detected, blocking submission."); // debug
                    Swal.fire({
                        title: `{{ translate('dashboard_reviews.error') }}`,
                        text: `{{ translate('dashboard_reviews.fill_the_reply_field') }}`,
                        icon: "error"
                    });
                    textarea.focus();
                    return false; // 🚨 stops execution
                }

                let formData = form.serialize();

                // Disable submit button to prevent double submission
                submitBtn.prop('disabled', true).text('Posting...');

                $.ajax({
                    url: form.attr('action'),
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.status == true) {
                            Swal.fire({
                                title: `{{ translate('dashboard_reviews.success') }}`,
                                text: `{{ translate('dashboard_reviews.reply_posted_successfully') }}`,
                                icon: "success"
                            }).then(() => {
                                $("#view_review_modal").modal('hide');
                                // Optionally reload the page or update the table
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = "An error occurred while posting the reply.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            title: "Error!",
                            text: errorMessage,
                            icon: "error"
                        });
                    },
                    complete: function() {
                        // Re-enable submit button
                        submitBtn.prop('disabled', false).text('Post Reply');
                    }
                });
            });

            $(document).on("click", ".edit_review_btn", function() {
                let review_id = $(this).attr("data-review-id");
                $("#edit_review_modal .modal-body").html(loader);
                $.ajax({
                    url: "{{ route('edit_review_details', '') }}" + "/" + review_id,
                    type: "GET",
                    success: function(response) {
                        if (response.status == true) {
                            $("#edit_review_modal .modal-body").html(response.data);
                            // Description CKEditor script starts from here
                            setTimeout(() => {
                                // if (typeof window[`editorInstanceDescription_${review_id}`] === 'undefined') {
                                let editorInstance = null;
                                ClassicEditor
                                    .create(document.querySelector(
                                        `#editor_description_${review_id}`), {
                                        enterMode: 'paragraph',
                                        shiftEnterMode: 'softBreak',
                                        toolbar: false,
                                    })
                                    .then(editor => {
                                        console.log('Editor was initialized',
                                            editor);
                                        window[
                                                `editorInstanceDescription_${review_id}`
                                            ] =
                                            editor;
                                        const maxLength = 2000;

                                        function getPlainText() {
                                            const editorData = editor.getData()
                                                .trim();
                                            const tempDiv = document.createElement(
                                                "div");
                                            tempDiv.innerHTML = editorData;
                                            return tempDiv.textContent || tempDiv
                                                .innerText;
                                        }

                                        function toggleNextButtonState() {
                                            const plainText = getPlainText();
                                            const remainingChars = plainText.length;
                                            window.commentLength = remainingChars;
                                            $('#char-counter').text(remainingChars +
                                                '/' + maxLength);
                                            $('#char-counter').css('color',
                                                remainingChars > maxLength ?
                                                'red' : '#949494');

                                            if (remainingChars < 30 &&
                                                remainingChars >= 1) {
                                                $('#char-counter').css('color',
                                                    'red');
                                            } else {
                                                $('#char-counter').css('color',
                                                    '#949494');
                                            }

                                            // Only enable .next if text is present and within limit
                                            const isTextValid = (remainingChars >
                                                    29 && remainingChars <=
                                                    maxLength) || remainingChars ===
                                                0;
                                            $(editor.ui.view.element)
                                                .closest('.review_comment')
                                                .find('button[type="submit"]')
                                                .prop('disabled', !isTextValid);
                                            window.validateRatings();
                                            console.log(remainingChars);
                                        }

                                        // Prevent input when max length is reached
                                        editor.editing.view.document.on(
                                            'beforeinput', (event, data) => {
                                                const editorData = editor
                                                    .getData();
                                                $(`#editor_description_${review_id}`)
                                                    .val(editorData);
                                                const plainText =
                                                    getPlainText();
                                                if (plainText.length >=
                                                    maxLength && data.inputType
                                                    .startsWith("insert")) {
                                                    event
                                                        .stop(); // Stop new input
                                                }
                                            });
                                        editor.editing.view.document.on('paste', (
                                            event, data) => {
                                            setTimeout(() => {
                                                const editorData =
                                                    editor
                                                    .getData();
                                                $(`#editor_description_${review_id}`)
                                                    .val(
                                                        editorData);
                                            }, 0);
                                        });
                                        // Monitor changes and enforce character limit
                                        editor.model.document.on('change:data',
                                            () => {
                                                const plainText =
                                                    getPlainText();
                                                if (plainText.length >
                                                    maxLength) {
                                                    editor.execute(
                                                        'undo'
                                                    ); // Revert last input
                                                }
                                                toggleNextButtonState
                                                    (); // Properly update button state
                                            });
                                        toggleNextButtonState();
                                    })
                                    .catch(error => {
                                        console.error(
                                            'There was a problem initializing the editor.',
                                            error);
                                    });
                                // }else{
                                //     console.log('CKEditor is already initialized.');
                                // }
                            }, 800);
                            // Description CKEditor script ends here


                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error"
                            });
                        }
                        window.validateRatings();
                        $(document).on("change", ".rating-checkbox", window.validateRatings);
                    }
                });
            });
            $(document).on('click', ".other-reason-btn", function() {
                let other_reason = $(this).attr("data-other-reason");
                $("#other_modal_body").html(other_reason);
            });


            // $(document).on('change', '#add_photo_file', function(event) {
            //     const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            //     const minSize = 300 * 1024;
            //     const maxSize = 10 * 1024 * 1024; // 10MB
            //     const maxFiles = 10; // number of images per upload
            //     let validImages = []; // Store valid images
            //     let validImageCount = 0;
            //     const abc;


            //     const files = event.target.files;
            //     if (files.length === 0) {
            //         alert("No files selected.");
            //         return;
            //     }

            //     const sortableContainer = $('.drag_drop_photos_wrapper');
            //     Swal.fire({
            //         title: "Uploading Images",
            //         html: `Uploading <b>0</b> out of ${files.length}`,
            //         allowOutsideClick: false,
            //         showConfirmButton: false,
            //         didOpen: () => {
            //             Swal.showLoading();
            //         },
            //     });
            //     let uploadedCount = 0;
            //     const totalFiles = files.length;


            //     // const files = Array.from(this.files);
            //     // Check if user is uploading more than 10 images
            //     if (files.length > maxFiles) {
            //         toastr.error(`❌ You can only upload a maximum of ${maxFiles} images at a time.`);
            //         this.value = ""; // Clear input field
            //         return;
            //     }
            //     for (let i = 0; i < files.length; i++) {
            //         let file = files[i];
            //         let reader = new FileReader();

            //         if (!validTypes.includes(file.type)) {
            //             if (!file.type.startsWith("image/")) {
            //                 continue;
            //             }
            //             // toastr.error(`❌ Invalid file type: ${file.name}`);
            //             previewImages(file, `❌ Invalid file type`);
            //             continue;
            //         }
            //         // Validate file size
            //         if (file.size < minSize || file.size > maxSize) {
            //             // toastr.error(`⚠️ File size not allowed: ${file.name} (must be 300KB-10MB)`);
            //             previewImages(file, `⚠️ File size not allowed (must be 300KB-10MB)`);
            //             continue;
            //         }
            //         // Store only valid files
            //         validImages.push(file);
            //         // previewImages(file);
            //     }
            //     // Update valid image count
            //     validImageCount = validImages.length;
            //     $("#validImageCount").text(validImageCount);
            //     this.value = "";
            //     setTimeout(function() {
            //         addBtn();
            //     }, 200);






            //     setTimeout(() => {
            //         if (sortableContainer.length && typeof Sortable !== "undefined") {
            //             new Sortable(sortableContainer[0], {
            //                 animation: 150,
            //                 draggable: '.sortable-element',
            //                 onEnd: function() {
            //                     const image_ids = sortableContainer
            //                         .children('.sortable-element')
            //                         .map(function() {
            //                             return $(this).data('image-id');
            //                         }).get();
            //                 },
            //             });
            //         }
            //     }, 500);
            //     Swal.fire({
            //         title: "Upload Complete",
            //         text: "All images have been uploaded successfully!",
            //         icon: "success",
            //         timer: 1500,
            //         showConfirmButton: false
            //     });
            //     // $('#add_photo_file').val('');
            // });


            $(document).on('change', '#add_photo_file', function(event) {
                const files = event.target.files;
                if (files.length === 0) {
                    alert("No files selected.");
                    return;
                }
                const sortableContainer = $('.drag_drop_photos_wrapper');
                Swal.fire({
                    title: "{{ translate('dashboard_reviews.uploading_images') }}",
                    html: `{{ translate('dashboard_reviews.uploading_progress', ['current' => '<b>0</b>', 'total' => '${files.length}']) }}`,
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    },
                });
                let uploadedCount = 0;
                const totalFiles = files.length;
                for (let i = 0; i < files.length; i++) {
                    let file = files[i];
                    let reader = new FileReader();
                    reader.onload = function(e) {
                        uploadedCount++;
                        Swal.update({
                            html: `{{ translate('dashboard_reviews.uploading_progress', ['current' => '<b>${uploadedCount}</b>', 'total' => '${totalFiles}']) }}`,
                        });
                        const newDiv = $(
                            `<div class="drag_drop_photo_single sortable-element">
                            <img alt="Preview ${i}" loading="lazy" src="${e.target.result}">
                            <div class="delete_btn_wrapper">
                                <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                            </div>
                        </div>`
                        );
                        const addPhotoBox = $(
                            '.drag_drop_photo_single.add_photo_box'
                        );
                        newDiv.insertBefore(addPhotoBox);
                    };
                    reader.readAsDataURL(file);
                }
                setTimeout(() => {
                    if (sortableContainer.length && typeof Sortable !== "undefined") {
                        new Sortable(sortableContainer[0], {
                            animation: 150,
                            draggable: '.sortable-element',
                            onEnd: function() {
                                const image_ids = sortableContainer
                                    .children('.sortable-element')
                                    .map(function() {
                                        return $(this).data('image-id');
                                    }).get();
                            },
                        });
                    }
                }, 500);
                Swal.fire({
                    title: "{{ translate('dashboard_reviews.upload_complete') }}",
                    text: "{{ translate('dashboard_reviews.all_images_uploaded') }}",
                    icon: "success",
                    timer: 1500,
                    showConfirmButton: false
                });
                // $('#add_photo_file').val('');
            });


            function previewImages(file, message = "") {
                const reader = new FileReader();
                // reader.onload = function(e) {
                //     const img = $(`
            //         <div class="image-preview col-md-4">
            //             <img src="${e.target.result}" alt="${file.name}">
            //             <span class="remove-image" data-name="${file.name}"><i class="fa fa-trash" aria-hidden="true"></i></span>
            //             ${errorMsg}
            //         </div>`);
                //     $('#imagePreview').append(img);
                //     updateImageUploadWrapperVisibility();
                // }
                // reader.readAsDataURL(file);

                reader.onload = function(e) {
                    uploadedCount++;
                    Swal.update({
                        html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
                    });
                    const errorMsg = message ? `<p class="error-message">${message}</p>` : "";
                    const newDiv = $(
                        `<div class="drag_drop_photo_single sortable-element">
                            <img alt="Preview ${i}" loading="lazy" src="${e.target.result}">
                            <div class="delete_btn_wrapper">
                                <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                            </div>
                        </div>`
                    );
                    const addPhotoBox = $(
                        '.drag_drop_photo_single.add_photo_box'
                    );
                    newDiv.insertBefore(addPhotoBox);
                };
                reader.readAsDataURL(file);

            }
            $(document).on('click', '.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn', function() {
                let listing_image = $(this);
                let imageId = listing_image.data('image-id');
                var listing_id = $("input[name='listing_id']").val();
                var review_id = $(this).data("review-id");
                Swal.fire({
                    title: "{{ translate('dashboard_reviews.are_you_sure') }}",
                    text: "{{ translate('dashboard_reviews.delete_confirmation') }}",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#ffce32",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "{{ translate('dashboard_reviews.yes_delete') }}"
                }).then((result) => {
                    if (result.isConfirmed) {
                        listing_image.closest('.drag_drop_photo_single').remove();
                        $.ajax({
                            url: `{{ route('review_delete_image', '') }}/${review_id}`,
                            type: "GET",
                            success: function(response) {
                                if (response.status == true) {
                                    Swal.fire({
                                        title: "{{ translate('dashboard_reviews.deleted') }}",
                                        text: "{{ translate('dashboard_reviews.file_deleted') }}",
                                        icon: "success"
                                    });
                                    form.closest("tr").remove();
                                } else {
                                    Swal.fire({
                                        title: "{{ translate('dashboard_reviews.error') }}",
                                        text: response.message,
                                        icon: "error"
                                    });
                                }
                            }
                        })
                    }
                });
            });
            window.validateRatings = function() {
                let allRated = true;
                $(".rating_star_wrapper").each(function() {
                    if (!$(this).find(".rating-checkbox:checked").length) {
                        allRated = false;
                    }
                });
                if (window.commentLength > 0 && window.commentLength < 30) {
                    allRated = false;
                }
                $('.review_comment button[type="submit"]').prop("disabled", !allRated);
            }
        });



        $(document).ready(function() {
            
            // $(document).on('click', '.reviews_index_topbar ul li a', function() {
            //     getActiveTab();

            //     var scrollTop = $(window).scrollTop();
            //     $(window).scrollTop(scrollTop);
            // });


            // Removed conflicting setActiveTab and getActiveTab functions
            // Tab activation is now handled by showTabByHash() function


            $(document).on('change', '#report_review [name="subject"]', function() {
                let input = $("#report_review input[name='subject']:checked").val();
                if (input == 'Other') {
                    $('.other_reason').removeClass('d-none');
                } else {
                    $('.other_reason').addClass('d-none');
                }
            });
        });


        Fancybox.bind("[data-fancybox='review-gallery']", {
            Toolbar: {
                display: [
                { id: "zoom", position: "left" },
                "slideshow",
                "fullscreen",
                "download",
                "thumbs",
                "close",
                ],
            },
        });


    </script>
@endpush
