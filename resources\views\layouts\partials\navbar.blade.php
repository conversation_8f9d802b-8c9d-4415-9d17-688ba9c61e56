<nav class="navbar navbar-default navbar-static-top m-b-0">
    <div class="navbar-header">
        <a class="navbar-toggle font-20 hidden-sm hidden-md hidden-lg " href="javascript:void(0)" data-toggle="collapse"
            data-target=".navbar-collapse">
            <i class="fa fa-bars"></i>
        </a>
        <div class="top-left-part">
            <a class="logo" href="{{ url('/' . app()->getLocale()) }}">
                <b>
                    <img src="{{ asset('') }}{{ App\CommonSetting::first()->dashboard_logo ?? '' }}" alt="home"
                        style="height:60px" />
                </b>
                <span class="favicon">
                    <img src="{{ asset('') }}{{ App\CommonSetting::first()->favicon ?? '' }}" alt="favicon"
                        class="dark-logo" style="height: 50px;width: 50px" />
                </span>
                <!-- <span>
                    <img src="{{ asset('') }}{{ App\CommonSetting::first()->dashboard_logo_text ?? '' }}" alt="homepage" class="dark-logo" style="height: 50px;width: 73px" />
                </span> -->
            </a>
        </div>
        <ul class="nav navbar-top-links navbar-left hidden-xs custom_class">
            @if (session()->get('theme-layout') != 'fix-header')
                <li class="sidebar-toggle">
                    <a href="javascript:void(0)" class="sidebartoggler font-20 waves-effect waves-light"><i
                            class="glyphicon glyphicon-menu-hamburger for_custom_icon fa fa-bars"></i></a>
                </li>
            @endif
            {{-- <li> --}}
            {{-- <form role="search" class="app-search hidden-xs"> --}}
            {{-- <i class="icon-magnifier"></i> --}}
            {{-- <input type="text" placeholder="Search..." class="form-control"> --}}
            {{-- </form> --}}
            {{-- </li> --}}
        </ul>
        <ul class="nav navbar-top-links navbar-right pull-right">
            {{-- Messages Icon --}}
            <li class="dropdown">
                <a class="waves-effect waves-light font-20 position-reltive" href="{{ route('message.dashboard_messenger') }}" tooltip="{{ translate('header.inbox') }}" flow="down">
                    <i class="fa-solid fa-envelope dark-yellow"></i>
                    <div class="badge position-absolute" id="message-badge" style="display: {{ auth()->user()->unread_messages_count() > 0 ? 'block' : 'none' }};">
                        <p class="fs-12" id="message-count">
                            {{ auth()->user()->unread_messages_count() > 9 ? '9+' : auth()->user()->unread_messages_count() }}
                        </p>
                    </div>
                </a>
            </li>
            {{-- Notifications Icon --}}
            <li class="dropdown noti_drop">
                <a class="dropdown-toggle waves-effect waves-light font-20 position-reltive notification" tooltip="{{ translate('header.notifications') }}" flow="down"
                    data-toggle="dropdown" href="javascript:void(0);">
                    <i class="fa-solid fa-bell dark-yellow"></i>
                    <div class="badge position-absolute">
                        <p class="fs-12">{{ isset(auth()->user()->unreadNotifications) ? count(auth()->user()->unreadNotifications) : ''}}</p>
                    </div>
                    {{-- <span class="badge badge-xs ">3</span>  --}}
                </a>
                {{-- animated slideInUp --}}
                <ul class="dropdown-menu dropdown-tasks">
                    @foreach (auth()->user()->notifications->take(4) as $notification)
                        <li class="media">
                            <div class="media-left">
                                @if ($notification->read_at == null)
                                    <i class="fa-solid fa-circle" style="color: #ffce32;"></i>
                                @endif
                            </div>
                            <div class="media-body">
                                @php
                                    $data = translateNotification($notification->data);
                                @endphp
                                <div class="media-heading justify-content-between align-items-center d-flex m-0">
                                    <p href="javascript:void(0);" style="color: #4A4A4A !important;"
                                        class="text-link m-0">{{ $data['title'] ?? '-' }}</p>
                                    <ul class="list-inline">
                                        <span class="font-normal com-time"
                                            style="color: #4A4A4A; font-family: 'Poppins-Medium'; font-size: 12px; font-weight: 400; line-height: 17px;">{{ $notification->created_at->format('d-M-Y') ?? '-' }}</span>
                                    </ul>
                                </div>
                                <a style="color: #9B9B9B; font-size: 13px;">{!! $data['message'] ?? '-' !!}</a>
                            </div>
                        </li>
                        <li class="divider"></li>
                    @endforeach
                    <li>
                        <a class="text-center" href="{{ url('notifications') }}">
                            <strong>{{ translate('dashboard.view_all') }} </strong>
                            <i class="fa fa-angle-right"></i>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="dropdown globe">
                <a class="dropdown-toggle waves-effect waves-light font-20 position-reltive notification" tooltip="{{ translate('header.language') }}" flow="down"
                data-toggle="dropdown" href="javascript:void(0);">
                    <img src="{{ asset('website') }}/images/translate-dashboard.svg" height="22px" width="22px">
                </a>
                <ul class="dropdown-menu animated flipInY" aria-labelledby="languageDropdown">
                    <li class="@if( app()->getLocale() == 'en') active @endif">
                        <a class="dropdown-item lang-pic d-flex align-items-center gap-1"
                            data-lang="English" onclick="return ChangeDashboardLanguage(this)"
                            href="javascript:void(0);">
                            <div class="flag_img">
                                <img width="20" height="15"
                                    src="{{ asset('website') }}/images/flags/us.png" alt="">
                            </div>
                            <p class="m-0 notranslate text-dark">{{ translate('home.english') }}</p>
                        </a>
                    </li>
                    <li class="@if( app()->getLocale() == 'es') active @endif">
                        <a class="dropdown-item lang-pic d-flex align-items-center gap-1"
                            onclick="return ChangeDashboardLanguage(this)" data-lang="Spanish"
                            href="javascript:void(0);">
                            <div class="flag_img">
                                <img width="20" height="15"
                                    src="{{ asset('website') }}/images/flags/co.png" alt="">
                            </div>
                            <p class="m-0 notranslate text-dark">{{ translate('home.spanish') }}</p>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="dropdown">
                <div class="dropdown user-pro-body ">
                    <div class="profile-image">
                        <a href="javascript:void(0);" class="dropdown-toggle u-dropdown text-blue"
                            data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                            <img src="{{ asset('website') . '/' . auth()->user()->avatar }}" alt="user-img"
                                class="img-circle">
                            {{-- <span class="badge badge-danger user_dropdown">
                                <i class="fa fa-angle-down"></i>
                            </span> --}}
                        </a>
                        <ul class="dropdown-menu animated flipInY">
                            <li><a href="{{ route('browse_listing') }}"><i class="fa-solid fa-user-tie"></i> {{ translate('dashboard.browse_website') }}</a></li>
                            {{-- <li><a href="{{ url('account-setting') }}"><i class="fa-solid fa-user-tie"></i> Personal Info </a> </li> --}}
                            <li><a href="{{ url('account-settings') }}"><i class="fa-solid fa-gear"></i> {{ translate('dashboard.settings') }} </a>
                            </li>
                            <li><a href="{{ url('logout') }}"><i class="fa fa-power-off"></i> {{ translate('dashboard.logout') }}</a></li>
                        </ul>
                    </div>
                </div>
            </li>
        </ul>
    </div>
</nav>

@push('js')
    {{-- <script>
        $(window).load(function(){
            $('.custom_class').click(function(){
                current_class =  $("body").attr('class');
                localStorage.setItem('currentClass', current_class);
            });
            var storedClass = localStorage.getItem('currentClass');
            $("body").addClass(storedClass);
            if(storedClass == 'normal'){
                $('.for_custom_icon').removeClass('fa fa-bars');
            }else{
                $('.for_custom_icon').addClass('fa fa-bars');
            }
        });
    </script> --}}
@endpush
