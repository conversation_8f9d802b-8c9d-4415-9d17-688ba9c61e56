@push('css')
    <style>
        .key_features_step .key_features_fields_wrapper .ck-editor__editable_inline {
            min-height: calc(1.5em * 5);
        }

        .key_features_step .key_features_fields_wrapper .ck .ck-placeholder:before,
        .key_features_step .key_features_fields_wrapper .ck.ck-placeholder:before {
            color: #A9AEC0;
            font-weight: 400
        }
    </style>
@endpush

<fieldset class="key_features_step custom_amenity_step" id="">
    <div class="inner_section_fieldset">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_main_col">
                    <div class="main_step_title">
                        <h2>{{ translate('stepper.missing_amenities') }}</h2>
                    </div>

                    <div class="step_description">
                        <p>{{ translate('stepper.missing_amenities_description') }}
                        </p>
                    </div>

                    <div class="key_features_fields_wrapper scrollable-section">
                        <div class="features_add_btn_wrapper">
                            <label for="">{{ translate('stepper.custom_amenities') }}</label>
                            <a class="add_feature_btn cust_add" href="javascript:void(0)">{{ translate('stepper.add') }}</a>
                        </div>
                        <div class="txt_field">
                            <input class="form-control key_features_title repeater1 no_validate" type="text"
                                placeholder="{{ translate('stepper.stepper_accomodation.key_feature_title') }}">
                        </div>
                        <textarea class="ck_editor no_validate" id="amenity_editor"
                            placeholder="{{ translate('stepper_accomodation.key_feature_description') }}"></textarea>
                        <div class="saved_features_list_wrapper mt-4">

                            @foreach ($listing->custom_amenities ?? [] as $custom_amenity)
                                <div class="single_saved_feature">
                                    <div class="content_cross_wrapper">
                                        <div class="title_description_wrapper w-100">
                                            <div class="feature_title">
                                                <h6>{{ $custom_amenity->name }}</h6>
                                                <input type="hidden" class="no_validate"
                                                    name="custom_amenities[{{ $loop->index }}][title]"
                                                    value="{{ $custom_amenity->name }}"
                                                    id="custom_amenity_title_{{ $loop->index }}">
                                            </div>
                                            <div class="feature_description">
                                                <p class=" fs-14 m-0">
                                                    <input type="hidden" class="no_validate"
                                                        name="custom_amenities[{{ $loop->index }}][description]"
                                                        value="{{ $custom_amenity->description }}"
                                                        id="custom_amenity_desc_{{ $loop->index }}">
                                                    {!! $custom_amenity->description !!}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="cross_icon_wrapper">
                                            <a class="remove_key_feature_btn" href="javascript:void(0)">
                                                <i class="fa fa-trash" aria-hidden="true"
                                                    style="font-size: 16px; color: red;"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1 " value="{{ translate('stepper.next') }}" />
    <input type="button" name="previous" class="previous action-button-previous" value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    {{-- <script src="https://cdn.ckeditor.com/ckeditor5/37.1.0/classic/ckeditor.js"></script> --}}
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    {{-- <script src="https://unpkg.com/@ckeditor/ckeditor5-inspector@4.1.0/build/inspector.js"></script> --}}

    <script>
        let amenityEditorInstance;

        ClassicEditor
            .create(document.querySelector('#amenity_editor'), {
                enterMode: 'paragraph',
                shiftEnterMode: 'softBreak',
                toolbar: false,
            })
            .then(editor => {
                console.log('Editor was initialized', editor);
                amenityEditorInstance = editor;
            })
            .catch(error => {
                console.error('There was a problem initializing the editor.', error);
            });


        $(document).ready(function() {
            var counter =
                {{ isset($listing->key_features) ? count($listing->key_features) : 0 }}; // Ensure counter is declared with 'var' to limit its scope

            $(document).on('click',
                '.custom_amenity_step .features_add_btn_wrapper .add_feature_btn',
                function() {
                    var title = $(this).closest('fieldset').find('.key_features_title').val().trim();
                    // var description = $(this).closest('fieldset').find('.key_features_description').val().trim();
                    var description = amenityEditorInstance.getData();
                    var plainText = $('<div>').html(description).text();

                    if (title !== "") {
                        var featureHTML = `
                        <div class="single_saved_feature">
                            <div class="content_cross_wrapper">
                                <div class="title_description_wrapper w-100">
                                    <div class="feature_title">
                                        <h6>${title}</h6>
                                        <input type="hidden" class="no_validate" name="custom_amenities[${counter}][title]" value="${title}" id="custom_amenity_title_${counter}">
                                    </div>
                                    <div class="feature_description">
                                        <p class=" fs-14 m-0">
                                            <input type="hidden" class="no_validate" name="custom_amenities[${counter}][description]" value="${description || @json(translate('stepper.no_description'))}" id="custom_amenity_desc_${counter}">
                                            ${plainText || @json(translate('stepper.no_description'))}
                                        </p>
                                    </div>
                                </div>
                                <div class="cross_icon_wrapper">
                                    <a class="remove_key_feature_btn" href="javascript:void(0)">
                                        <i class="fa fa-trash" aria-hidden="true" style="font-size: 16px; color: red;"></i>
                                    </a>
                                </div>
                            </div>
                        </div>`;

                        $('.custom_amenity_step .saved_features_list_wrapper').append(
                            featureHTML);

                        $(this).closest('fieldset').find('.key_features_title').val('');
                        // $(this).closest('fieldset').find('.key_features_description').val('');
                        if (amenityEditorInstance) {
                            amenityEditorInstance.setData(''); // Set content to an empty string
                        }
                        counter++;
                    } else {
                        Swal.fire({
                            title: @json(translate('stepper.error')),
                            text: @json(translate('stepper.fill_fields_first')),
                            icon: "error"
                        });
                    }
                });
            $(document).on('click', '.custom_amenity_step .remove_key_feature_btn',
                function() {
                    $(this).closest('.single_saved_feature').remove();
                    reindexName();

                    if ($('input[name="itineraries[0][title]"]').length > 0) {
                        console.log(@json(translate('stepper.input_exists')));
                    } else {
                        $('.itinerary_parent .txt_field input').removeClass('no_validate');
                        $('.itinerary_parent .txt_field textarea').removeClass('no_validate');
                    }
                });

            function reindexName() {
                $('.custom_amenity_step .saved_features_list_wrapper .single_saved_feature')
                    .each(function(index) {
                        $(this).find('input').each(function() {
                            var name = $(this).attr('name');
                            var newName = name.replace(/\[\d+\]/, '[' + index + ']');
                            $(this).attr('name', newName);

                            var id = $(this).attr('id');
                            var baseId = id.substring(0, id.lastIndexOf('_') + 1);
                            var newId = baseId + index;
                            $(this).attr('id', newId);
                        });
                    });
                counter = $(
                    '.custom_amenity_step .saved_features_list_wrapper .single_saved_feature'
                ).length;
            }
        });
    </script>
@endpush
