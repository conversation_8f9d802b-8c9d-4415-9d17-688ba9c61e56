/**
 * =============================================
 * LUXUSTARS MESSENGER SYSTEM
 * Complete chat functionality with real-time messaging
 * =============================================
 */

// =============================================
// GLOBAL VARIABLES
// =============================================

// HTML escape function to prevent XSS attacks
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// HTML decode function to display content as user typed it
function decodeHtml(encoded) {
    return encoded
        .replace(/&amp;/g, "&")
        .replace(/&lt;/g, "<")
        .replace(/&gt;/g, ">")
        .replace(/&quot;/g, '"')
        .replace(/&#039;/g, "'");
}
let auth_id = null;
let pusher = null;
let current_channel = null;
let notification_channel = null;
let isLoadingMoreMessages = false;
let currentConversationIds = null;
let scrollDebounceTimer = null;
let conversationRefreshTimer = null;
let isLoadingConversations = false;
let currentFilter = 'All'; // Track current conversation filter
let isInitializingConversation = false; // Prevent duplicate conversation loading

// Typing indicator variables
let typingTimer = null;
let isTyping = false;
let typingUsers = new Set(); // Track who is currently typing

// Routes - will be set from window object
let routes = {
    getConversations: null,
    fetchConversation: null,
    parseMessage: null,
    loadMoreMessages: null,
    messageIndex: null,
    searchMessages: null
};

// Message search variables
let isSearching = false;
let searchResults = [];
let currentSearchQuery = '';
let currentSearchIndex = -1;

// =============================================
// UTILITY FUNCTIONS
// =============================================

/**
 * Set dynamic height for scrollable sections
 */
function setDynamicHeight() {
    var screenHeight = $(window).height();
    // console.log(screenHeight);
    var scrollableSections = $('.messenger_main_sec .scrollable-section');
    var scrollableSections2 = $('.messenger_main_sec .scrollable-section-2');

    if (scrollableSections.length >= 2) {
        scrollableSections.each(function(index, element) {
            var sectionTop = $(element).offset().top;
            // console.log(`Section ${index + 1} top:`, sectionTop);
            var heightTopResult = screenHeight - sectionTop;
            // console.log(`Height Top Result for Section ${index + 1}:`, heightTopResult);
            heightTopResult = heightTopResult;  // -20 was here previously (by Rameel)
            $(element).css('height', heightTopResult);
            $(element).css('max-height', heightTopResult);
            // console.log(`Total screen height for Section ${index + 1}:`, heightTopResult);
        });
    } else {
        var sectionTop = $('fieldset.active .scrollable-section').offset().top;
        // console.log(sectionTop);
        var heightTopResult = screenHeight - sectionTop;
        // console.log(heightTopResult);
        heightTopResult = heightTopResult;  // -20 was here previously (by Rameel)
        $('fieldset.active .scrollable-section').css('height', heightTopResult);
        $('fieldset.active .scrollable-section').css('max-height', heightTopResult);
        // console.log("Total screen height:", heightTopResult);
    }

    if (scrollableSections2.length > 0) {
            var sectionTop = $('.messenger_main_sec .scrollable-section-2').offset().top;
            // console.log(sectionTop);
            var heightTopResult = screenHeight - sectionTop;
            // console.log(heightTopResult);
            heightTopResult = heightTopResult - 75;
            $('.messenger_main_sec .scrollable-section-2').css('height', heightTopResult);
            $('.messenger_main_sec .scrollable-section-2').css('max-height', heightTopResult);
            // console.log("Total screen height:", heightTopResult);
    }

}

/**
 * Scroll to bottom of chat container
 */
function scrollToBottom() {
    const chatContainer = $(".chat_body.scrollable-section-2");
    if (chatContainer.length) {
        const scrollHeight = chatContainer[0].scrollHeight;
        chatContainer.scrollTop(scrollHeight);
    }
}

/**
 * Loading spinner HTML
 */
const spinner = `
    <div class="text-center">
        <div class="spinner-border " role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
`;

// =============================================
// TYPING INDICATOR FUNCTIONS
// =============================================

/**
 * Send typing status to server
 */
function sendTypingStatus(typing) {
    if (!currentConversationIds || !routes.typing) {
        return;
    }

    const typingUrl = routes.typing.replace('CONVERSATION_ID', currentConversationIds);

    $.ajax({
        url: typingUrl,
        method: 'POST',
        data: {
            typing: typing,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            console.log(`📝 Typing status sent: ${typing}`);
        },
        error: function(xhr) {
            console.error('❌ Error sending typing status:', xhr);
        }
    });
}

/**
 * Handle typing start
 */
function startTyping() {
    if (!isTyping && currentConversationIds) {
        isTyping = true;
        sendTypingStatus(true);
        console.log('📝 Started typing');
    }
}

/**
 * Handle typing stop
 */
function stopTyping() {
    // Clear the typing timer
    if (typingTimer) {
        clearTimeout(typingTimer);
        typingTimer = null;
        console.log('📝 Cleared typing timer on stop');
    }

    if (isTyping && currentConversationIds) {
        isTyping = false;
        sendTypingStatus(false);
        console.log('📝 Stopped typing - sent status to server');
    } else if (isTyping) {
        // Reset typing state even if no conversation
        isTyping = false;
        console.log('📝 Reset typing state (no active conversation)');
    }
}

/**
 * Reset typing timer
 */
function resetTypingTimer() {
    // Clear any existing timer
    if (typingTimer) {
        clearTimeout(typingTimer);
        console.log('📝 Cleared existing typing timer');
    }

    // Set new timer to stop typing after 1.5 seconds of inactivity
    typingTimer = setTimeout(() => {
        console.log('📝 Typing timer expired - stopping typing');
        stopTyping();
    }, 1500); // Increased to 1.5 seconds for better UX

    console.log('📝 Reset typing timer (1.5s)');
}

/**
 * Display typing indicator
 */
function showTypingIndicator(userName) {
    typingUsers.add(userName);
    updateTypingIndicatorDisplay();
}

/**
 * Hide typing indicator
 */
function hideTypingIndicator(userName) {
    typingUsers.delete(userName);
    console.log(`📝 Removed ${userName} from typing users. Remaining:`, Array.from(typingUsers));
    updateTypingIndicatorDisplay();

    // Also set a timeout to force hide the indicator after 3 seconds
    // This is a fallback in case the normal flow doesn't work
    setTimeout(() => {
        if (typingUsers.has(userName)) {
            console.log(`📝 Force removing ${userName} from typing users (timeout)`);
            typingUsers.delete(userName);
            updateTypingIndicatorDisplay();
        }
    }, 3000);
}

/**
 * Update typing indicator display
 */
function updateTypingIndicatorDisplay() {
    const typingContainer = $('#typing-indicator-container');

    console.log('📝 Updating typing indicator display. Users typing:', Array.from(typingUsers));

    if (typingUsers.size === 0) {
        console.log('📝 No users typing - hiding indicator');
        typingContainer.hide().empty();
        return;
    }

    let typingText = '';
    const usersArray = Array.from(typingUsers);

    if (usersArray.length === 1) {
        typingText = `${usersArray[0]} is typing...`;
    } else if (usersArray.length === 2) {
        typingText = `${usersArray[0]} and ${usersArray[1]} are typing...`;
    } else {
        typingText = `${usersArray[0]} and ${usersArray.length - 1} others are typing...`;
    }

    console.log('📝 Showing typing indicator:', typingText);

    typingContainer.html(`
        <div class="typing-indicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span class="typing-text">${typingText}</span>
        </div>
    `).show();
}

// =============================================
// PUSHER REAL-TIME MESSAGING FUNCTIONS
// =============================================

/**
 * Initialize Pusher connection
 */
function initializePusher() {
    // Enable pusher logging - don't include this in production
    Pusher.logToConsole = true;

    pusher = new Pusher('31c9c69757421fc996e0', {
        cluster: 'ap1',
    });

    // Subscribe to global notification channel for unread count updates
    notification_channel = pusher.subscribe('luxustars-chat-notification-' + auth_id);
    notification_channel.bind('message.notification', function(data) {
        console.log('Global notification received:', data);

        // Only update unread count if the message is not for the currently active conversation
        if (currentConversationIds !== data.conversation_ids) {
            incrementUnreadCount(data.conversation_ids);
            // Update conversation preview and timestamp in real-time
            updateConversationPreview(data.conversation_ids, data.message, data.timestamp);
            console.log(`📩 New message from ${data.sender_name}: ${data.message}`);
        } else {
            console.log('Message is for active conversation, handled by conversation channel');
        }
    });
}

/**
 * Subscribe to specific conversation channel
 */
function subscribeToChannel(conversation_ids) {
    if (current_channel) {
        pusher.unsubscribe(current_channel.name);
    }

    // Subscribe to the channel
    current_channel = pusher.subscribe('luxustars-chat-' + conversation_ids);

    current_channel.bind('message.sent', function(data) {
        if (auth_id == data.receiver_id) {
            // Check if this message is for the currently active conversation
            if (currentConversationIds === data.conversation_id) {
                // User is in the active conversation - show message immediately
                handleIncomingMessage(data);
            } else {
                // User is not in the active conversation - increment unread count
                incrementUnreadCount(data.conversation_id);
            }

            // Update conversation preview and timestamp in real-time
            updateConversationPreview(data.conversation_id, data.message, data.timestamp);
        }
    });

    // Listen for typing events
    current_channel.bind('user.typing', function(data) {
        // Only show typing indicator if it's not from the current user
        if (data.user_id !== auth_id && currentConversationIds === data.conversation_id) {
            if (data.typing == "true") {
                showTypingIndicator(data.user_name);
                console.log(`📝 ${data.user_name} is typing...`);
            } else {
                hideTypingIndicator(data.user_name);
                console.log(`📝 ${data.user_name} stopped typing`);
            }
        }
    });
}

/**
 * Handle incoming message for active conversation
 */
function handleIncomingMessage(data) {
    // Play notification sound with error handling and multi-tab coordination
    const messageId = `${auth_id}_${data.timestamp || Date.now()}`;
    const notificationKey = `audio_played_${messageId}`;

    // Check if audio was already played in another tab
    if (!localStorage.getItem(notificationKey)) {
        // Mark that we're about to play audio
        localStorage.setItem(notificationKey, Date.now().toString());

        const audio = document.getElementById('notification-audio');
        if (audio) {
            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.catch(error => {
                    console.log('Audio play failed:', error);
                });
            }
        }
    }

    // Get current date for message
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    $("#chat-body-container").append(
        `<div class="msg msg_left">
            <div class="chat_bubble">
                <p class="">${escapeHtml(decodeHtml(data.message))}</p>
                <span class="message-time">${timeString}</span>
            </div>
        </div>`
    );

    // Auto scroll to bottom
    $("#chat-body-container").scrollTop($("#chat-body-container")[0].scrollHeight);
}

// =============================================
// UNREAD COUNT MANAGEMENT FUNCTIONS
// =============================================

/**
 * Update unread count for a specific conversation
 */
function updateUnreadCount(conversationIds, count) {
    const conversationElement = $(`.conversation-user[data-conversation-ids="${conversationIds}"]`);

    if (count > 0) {
        // Add unread styling
        conversationElement.addClass('has-unread');
        conversationElement.attr('data-unread-count', count);

        // Update profile picture badge
        const profilePicture = conversationElement.find('.user_profile_picture');
        let badge = profilePicture.find('.unread-count-badge');
        if (badge.length === 0) {
            badge = $('<div class="unread-count-badge"></div>');
            profilePicture.append(badge);
        }
        badge.text(count);

        // Update text styling
        conversationElement.find('h6, span, p').addClass('unread-text');

        console.log(`Updated unread count for conversation ${conversationIds}: ${count}`);

        // Update/add count in date area
        const dateWrapper = conversationElement.find('.chat_date_unread_wrapper');
        let countElement = dateWrapper.find('.unread-count-text');
        if (countElement.length === 0) {
            countElement = $('<div class="unread-count-text"><span class="unread-count-number"></span></div>');
            dateWrapper.append(countElement);
        }
        countElement.find('.unread-count-number').text(count);
    } else {
        // Remove unread styling
        conversationElement.removeClass('has-unread');
        conversationElement.attr('data-unread-count', '0');
        conversationElement.find('.unread-count-badge').remove();
        conversationElement.find('.unread-count-text').remove();
        conversationElement.find('.unread-text').removeClass('unread-text');
    }
}

/**
 * Increment unread count when new message is received
 */
function incrementUnreadCount(conversationIds) {
    const conversationElement = $(`.conversation-user[data-conversation-ids="${conversationIds}"]`);
    const currentCount = parseInt(conversationElement.attr('data-unread-count') || '0');
    updateUnreadCount(conversationIds, currentCount + 1);
}

/**
 * Get time ago string for timestamps
 */
function getTimeAgo(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} min${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
        // For older messages, show the actual date
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
        });
    }
}

/**
 * Update conversation preview and timestamp in real-time
 */
function updateConversationPreview(conversationIds, message, timestamp) {
    const conversationElement = $(`.conversation-user[data-conversation-ids="${conversationIds}"]`);

    if (conversationElement.length === 0) {
        console.log('⚠️ Conversation not found in sidebar, refreshing conversations:', conversationIds);
        // If conversation not found, fall back to refreshing the list
        refreshConversations(1000);
        return;
    }

    // Move conversation to top of the list (most recent)
    const conversationContainer = conversationElement.parent();
    conversationElement.detach().prependTo(conversationContainer);

    // Update message preview
    const previewElement = conversationElement.find('.chat_preview p');
    if (previewElement.length) {
        // Truncate message if too long
        const truncatedMessage = message.length > 50 ? message.substring(0, 50) + '...' : message;
        previewElement.text(truncatedMessage);

        // Add unread styling if this conversation is not currently active
        if (currentConversationIds !== conversationIds) {
            previewElement.addClass('unread-text');
        }
    }

    // Update timestamp
    const timestampElement = conversationElement.find('.chat_date span');
    if (timestampElement.length && timestamp) {
        const messageDate = new Date(timestamp);
        const timeAgo = getTimeAgo(messageDate);
        timestampElement.text(timeAgo);

        // Add unread styling if this conversation is not currently active
        if (currentConversationIds !== conversationIds) {
            timestampElement.addClass('unread-text');
        }
    }

    console.log(`✅ Updated conversation preview for ${conversationIds}: "${message}"`);
}

/**
 * Get total unread count across all conversations
 */
function getTotalUnreadCount() {
    let total = 0;
    $('.conversation-user[data-unread-count]').each(function() {
        const count = parseInt($(this).attr('data-unread-count') || '0');
        total += count;
    });
    return total;
}

/**
 * Update header badge from current page unread counts
 */
function updateHeaderBadgeFromPage() {
    const totalUnread = getTotalUnreadCount();
    if (window.updateHeaderBadge) {
        window.updateHeaderBadge(totalUnread);
        console.log('📊 Updated header badge from page:', totalUnread);
    }
}

// =============================================
// CONVERSATION MANAGEMENT FUNCTIONS
// =============================================

/**
 * Debounced version of getConversations to prevent multiple rapid calls
 */
function refreshConversations(delay = 500) {
    // Clear existing timer
    if (conversationRefreshTimer) {
        clearTimeout(conversationRefreshTimer);
    }

    // Set new timer
    conversationRefreshTimer = setTimeout(() => {
        getConversations();
    }, delay);
}

/**
 * Get conversations (get the sidebar conversations)
 */
function getConversations(name = null, filter = null) {
    // Prevent multiple simultaneous calls
    if (isLoadingConversations) {
        console.log('⏳ Conversations already loading, skipping...');
        return;
    }

    isLoadingConversations = true;
    let chat_threads_container = $("#chat-threads-container");

    // Prepare data object
    let requestData = {};
    if (name) requestData.name = name;
    if (filter) requestData.filter = filter;

    $.ajax({
        url: routes.getConversations,
        data: requestData,
        success: function(response) {
            if (response.status == true) {
                chat_threads_container.html(response.data);

                // Re-apply active conversation styling after refresh
                if (currentConversationIds) {
                    setTimeout(() => {
                        $(`.conversation-user[data-conversation-ids="${currentConversationIds}"]`)
                            .addClass("active-conversation")
                            .siblings()
                            .removeClass("active-conversation");
                    }, 100);
                }
            }
        },
        error: function(xhr) {
            chat_threads_container.html('Something went wrong. Please try again.');
        },
        complete: function() {
            isLoadingConversations = false;
        }
    });
}

/**
 * Get user conversation info
 */
function getUserConversation(conversation_ids, receiver_id = null) {
    console.log('🔄 Loading conversation UI for:', conversation_ids);

    // Prevent duplicate calls for the same conversation
    if (getUserConversation.loading === conversation_ids) {
        console.log('⚠️ Conversation UI already loading for:', conversation_ids);
        return;
    }

    getUserConversation.loading = conversation_ids;

    let message_container = $("#message-container");
    $.ajax({
        url: routes.fetchConversation,
        data: {
            conversation_ids,
            receiver_id
        },
        success: function(response) {
            console.log('📋 Conversation UI response:', response.status);

            if (response.status == true) {
                message_container.html(response.data);
                // Load messages after the conversation UI is ready
                setTimeout(() => {
                    parseMessage(conversation_ids);
                }, 100);
            } else {
                message_container.html(response.message);
                console.error('❌ Failed to load conversation UI:', response.message);
            }
        },
        complete: function() {
            setDynamicHeight();
            getUserConversation.loading = null; // Reset loading flag
        },
        error: function(xhr) {
            console.error('❌ Error loading conversation UI:', xhr);
            message_container.html('Something went wrong. Please try again.');
        }
    });
}

/**
 * Activate conversation
 */
function activeConversation(conversation_ids) {
    console.log('🎯 Activating conversation:', conversation_ids);

    // Prevent duplicate calls
    if (isInitializingConversation && currentConversationIds === conversation_ids) {
        console.log('⚠️ Conversation already being initialized, skipping duplicate call');
        return;
    }

    isInitializingConversation = true;

    // Reset pagination state
    isLoadingMoreMessages = false;
    currentConversationIds = conversation_ids;

    // Clear typing indicators when switching conversations
    typingUsers.clear();
    updateTypingIndicatorDisplay();
    stopTyping(); // Stop our own typing status

    // Reset unread count for the selected conversation
    updateUnreadCount(conversation_ids, 0);

    // Update active conversation styling
    const conversationElement = $(`.conversation-user[data-conversation-ids="${conversation_ids}"]`);
    console.log('📍 Found conversation element:', conversationElement.length);

    conversationElement.addClass("active-conversation").siblings().removeClass("active-conversation");

    // Subscribe to Pusher channel
    subscribeToChannel(conversation_ids);

    // Load conversation UI and messages
    getUserConversation(conversation_ids);

    // Update header badge when opening conversation (messages will be marked as read)
    setTimeout(() => {
        updateHeaderBadgeFromPage();
    }, 1000);

    // Initialize scroll pagination after conversation is fully loaded
    setTimeout(() => {
        console.log('🔄 Setting up scroll pagination for conversation:', conversation_ids);
        handleScrollUpPagination();
        isInitializingConversation = false; // Reset flag after initialization
    }, 2000);

    // Refresh conversation list after a short delay to update read status
    refreshConversations(1000);

    console.log('✅ Conversation activation complete');
}

// =============================================
// MESSAGE PARSING AND PAGINATION FUNCTIONS
// =============================================

/**
 * Parse Message (get the message of the conversation) - Initial load only
 */
function parseMessage(conversation_ids) {
    let message_container = $("#chat-body-container");

    // Prevent duplicate calls for the same conversation
    if (parseMessage.loading === conversation_ids) {
        console.log('⚠️ Messages already loading for:', conversation_ids);
        return;
    }

    parseMessage.loading = conversation_ids;

    window.checkMessageField();

    console.log('📥 Loading initial messages for conversation:', conversation_ids);

    $.ajax({
        url: routes.parseMessage.replace('CONVERSATION_ID', conversation_ids),
        data: {
            page: 1,
            per_page: 20  // Initial load: 20 messages (consistent with load more)
        },
        success: function(response) {
            if (response.status == true) {
                console.log('📥 Initial messages loaded:', response.pagination);

                // Initial load - replace all content
                message_container.html(response.data);

                // Update pagination info
                updatePaginationInfo(response.pagination);

                // Reset loading flag
                isLoadingMoreMessages = false;

                // Debug: Log initial pagination state
                console.log('📊 Initial pagination state after load:', {
                    hasMore: $(".load-more-messages-indicator").attr('data-has-more'),
                    nextPage: $(".load-more-messages-indicator").attr('data-next-page'),
                    totalMessages: response.pagination.total,
                    currentPage: response.pagination.current_page,
                    hasMorePages: response.pagination.has_more_pages
                });

                // Setup scroll pagination after messages are loaded
                setTimeout(() => {
                    handleScrollUpPagination();
                    console.log('🔄 Scroll pagination setup complete');
                }, 300);

                // Scroll to bottom after loading
                setTimeout(() => {
                    scrollToBottom();
                    console.log('⬇️ Scrolled to bottom after initial load');
                }, 500);
            }
        },
        error: function(xhr) {
            console.log('❌ Error loading initial messages:', xhr);
            message_container.html('<div class="text-center py-3"><small class="text-danger">Error loading messages</small></div>');
        },
        complete: function() {
            parseMessage.loading = null; // Reset loading flag
        }
    });
}

/**
 * Update pagination information
 */
function updatePaginationInfo(pagination) {
    const indicator = $(".load-more-messages-indicator");

    console.log('🔄 Updating pagination info:', pagination);
    console.log('📍 Indicator found:', indicator.length);

    // Ensure we have proper number values
    const currentPage = parseInt(pagination.current_page) || 1;
    const hasMorePages = pagination.has_more_pages === true || pagination.has_more_pages === 'true';

    // if (hasMorePages) {
    //     const nextPage = currentPage + 1;
    //     indicator.attr('data-has-more', 'true');
    //     indicator.attr('data-next-page', nextPage);

    //     // Reset loading indicator content
    //     indicator.html(`
    //         <div class="text-center py-2">
    //             <div class="spinner-border spinner-border-sm" role="status">
    //                 <span class="visually-hidden">{{ @json(translate('chatbox.load_more_msg_txt')) }}</span>
    //             </div>
    //             <small class="text-muted ms-2">{{ @json(translate('chatbox.load_more_msg_txt')) }}</small>
    //         </div>
    //     `);
    //     console.log(`✅ Set has_more=true, next_page=${nextPage}`);
    // } else {
    //     indicator.attr('data-has-more', 'false');
    //     indicator.html('<div class="text-center py-2"><small class="text-muted">No more messages</small></div>');
    //     console.log('🚫 Set has_more=false - no more messages');
    // }
    const loadMoreMsg = $('#indicator').data('load-more-msg');
    const noMoreMsg = 'No more messages'; // Default message if no translation is passed

    if (hasMorePages) {
        const nextPage = currentPage + 1;
        $('#indicator').attr('data-has-more', 'true');
        $('#indicator').attr('data-next-page', nextPage);

        // Reset loading indicator content
        $('#indicator').html(`
            <div class="text-center py-2">
                <div class="spinner-border spinner-border-sm" role="status">
                    <span class="visually-hidden">${loadMoreMsg}</span>
                </div>
                <small class="text-muted ms-2">${loadMoreMsg}</small>
            </div>
        `);

        console.log(`✅ Set has_more=true, next_page=${nextPage}`);
    } else {
        $('#indicator').attr('data-has-more', 'false');
        $('#indicator').html(`
            <div class="text-center py-2">
                <small class="text-muted">${noMoreMsg}</small>
            </div>
        `);
        console.log('🚫 Set has_more=false - no more messages');
    }

    console.log('📊 Final indicator state:', {
        hasMore: indicator.attr('data-has-more'),
        nextPage: indicator.attr('data-next-page'),
        currentPage: currentPage,
        hasMorePages: hasMorePages
    });
}

/**
 * Handle scroll-up pagination
 */
function handleScrollUpPagination() {
    const chatContainer = $(".chat_body.scrollable-section-2");

    console.log('🔧 Setting up scroll pagination...');
    console.log('📦 Chat container found:', chatContainer.length);
    console.log('📏 Container height:', chatContainer.length ? chatContainer[0].scrollHeight : 'N/A');
    console.log('📍 Load indicator:', $(".load-more-messages-indicator").length);

    if (chatContainer.length) {
        // Remove existing scroll handlers to prevent duplicates
        chatContainer.off('scroll.pagination');
        console.log('🗑️ Removed existing scroll handlers');

        chatContainer.on('scroll.pagination', function() {
            // Clear previous debounce timer
            clearTimeout(scrollDebounceTimer);

            scrollDebounceTimer = setTimeout(() => {
                const scrollTop = $(this).scrollTop();
                const scrollHeight = $(this)[0].scrollHeight;
                const clientHeight = $(this)[0].clientHeight;
                const hasMore = $(".load-more-messages-indicator").attr('data-has-more') === 'true';
                const nextPage = parseInt($(".load-more-messages-indicator").attr('data-next-page')) || 2;

                // Debug scroll detection - only log when near top
                if (scrollTop <= 100) {
                    console.log(`📜 Scroll - Top: ${scrollTop}, Height: ${scrollHeight}, Client: ${clientHeight}, Has more: ${hasMore}, Next page: ${nextPage}, Loading: ${isLoadingMoreMessages}, ConversationId: ${currentConversationIds}`);
                }

                // Check if user scrolled near the top and there are more messages to load
                if (scrollTop <= 50 && hasMore && !isLoadingMoreMessages && currentConversationIds) {
                    isLoadingMoreMessages = true;

                    console.log(`🔄 Loading more messages - Page: ${nextPage}`);

                    // Show loading indicator
                    $(".load-more-messages-indicator").show();

                    // Load more messages
                    loadMoreMessages(currentConversationIds, nextPage);
                }
            }, 150); // Reduced debounce for better responsiveness
        });
    } else {
        console.log('❌ Chat container not found for scroll pagination');
    }
}

/**
 * Load more messages for pagination
 */
function loadMoreMessages(conversation_ids, page) {
    const chatContainer = $(".chat_body.scrollable-section-2");

    console.log(`🔄 Loading more messages - Page: ${page}, Conversation: ${conversation_ids}`);

    $.ajax({
        url: routes.loadMoreMessages.replace('CONVERSATION_ID', conversation_ids),
        data: {
            page: page,
            per_page: 20
        },
        success: function(response) {
            console.log('📥 Load more messages response:', response);

            if (response.status == true && response.data.trim() !== '') {
                // Store current scroll position
                const oldScrollHeight = chatContainer[0].scrollHeight;
                const oldScrollTop = chatContainer.scrollTop();

                console.log(`📏 Before: scrollHeight=${oldScrollHeight}, scrollTop=${oldScrollTop}`);

                // Prepend new messages after the loading indicator
                $(".load-more-messages-indicator").after(response.data);

                // Remove duplicate date separators
                removeDuplicateDateSeparators();

                // Calculate new scroll position to maintain user's view
                const newScrollHeight = chatContainer[0].scrollHeight;
                const scrollDifference = newScrollHeight - oldScrollHeight;

                // Set new scroll position to maintain user's view
                chatContainer.scrollTop(oldScrollTop + scrollDifference);

                console.log(`📏 After: scrollHeight=${newScrollHeight}, scrollTop=${chatContainer.scrollTop()}, difference=${scrollDifference}`);

                // Update pagination info
                updatePaginationInfo(response.pagination);

                console.log(`✅ Loaded page ${page}, scroll adjusted by ${scrollDifference}px`);

                // Debug: Check state after update
                setTimeout(() => {
                    const newState = {
                        hasMore: $(".load-more-messages-indicator").attr('data-has-more'),
                        nextPage: $(".load-more-messages-indicator").attr('data-next-page')
                    };
                    console.log('📊 State after pagination update:', newState);
                }, 100);
            } else {
                // No more messages
                $(".load-more-messages-indicator").attr('data-has-more', 'false');
                $(".load-more-messages-indicator").html('<div class="text-center py-2"><small class="text-muted">No more messages</small></div>');
                console.log('🚫 No more messages to load');
            }
        },
        error: function(xhr) {
            console.log('❌ Error loading more messages:', xhr);
            // Update indicator to show error
            $(".load-more-messages-indicator").html('<div class="text-center py-2"><small class="text-danger">Error loading messages</small></div>');
        },
        complete: function() {
            // Always reset loading flag and hide indicator
            isLoadingMoreMessages = false;
            $(".load-more-messages-indicator").hide();
            console.log('🔄 Load more messages complete, reset loading flag');
        }
    });
}

/**
 * Remove duplicate date separators
 */
function removeDuplicateDateSeparators() {
    const dateElements = $("#chat-body-container .chat_date_time");
    let previousDate = null;

    dateElements.each(function() {
        const currentDate = $(this).find('p').text();
        if (currentDate === previousDate) {
            $(this).remove();
        } else {
            previousDate = currentDate;
        }
    });
}

// =============================================
// MESSAGE SENDING FUNCTIONS
// =============================================

/**
 * Send message form handler
 */
function handleMessageSend(form) {
    // Get message text and trim whitespace
    let messageText = form.find("input[name='message']").val().trim();
    let hasImage = $("#image-input").val();

    // Prevent empty messages (unless there's an image)
    if (!messageText && !hasImage) {
        return false;
    }

    // Prevent multiple rapid submissions
    if (form.hasClass('submitting')) {
        return false;
    }

    // Mark form as submitting
    form.addClass('submitting');

    // Stop typing immediately when sending message
    stopTyping();

    // 1. Instantly add message to chat before AJAX
    let messageId = null;
    if (messageText) {
        messageId = addInstantMessage(messageText);
    }

    // 2. Reset form immediately for next message
    $("#message-form")[0].reset();
    $("input[name='message']").attr('placeholder', 'Type your message....');
    $(".send_msg_btn").removeClass('file-selected');
    $(".send_msg_btn").html('<i class="fas fa-paper-plane"></i>');
    $(".send_msg_btn").prop('disabled', false);

    // 3. Scroll to bottom
    setTimeout(function() {
        scrollToBottom();
    }, 50);

    let url = form.attr("action");
    let data = new FormData();
    data.append('_token', $('meta[name="csrf-token"]').attr('content'));
    data.append('message', messageText);
    if (hasImage && $("#image-input")[0].files[0]) {
        data.append('attachment', $("#image-input")[0].files[0]);
    }

    $.ajax({
        url: url,
        method: "POST",
        data: data,
        dataType: "JSON",
        processData: false,
        contentType: false,
        beforeSend: function() {
            // Show loading state for image uploads
            if ($("#image-input").val()) {
                $(".send_msg_btn").html('<i class="fas fa-spinner fa-spin"></i>');
                $(".send_msg_btn").prop('disabled', true);
            }
        },
        success: function(response) {
            if (response.status == true) {
                // Remove clock icon from the message
                if (messageId) {
                    $(`#${messageId} .fa-clock`).remove();
                }

                // Update conversation preview with the sent message
                if (currentConversationIds) {
                    const messageText = response.data.message || 'Sent an image';
                    updateConversationPreview(currentConversationIds, `You: ${messageText}`, response.data.created_at);
                }
            }

            // Remove submitting class
            form.removeClass('submitting');
        },
        error: function(_xhr, _status, error) {
            // Reset button state on error
            $(".send_msg_btn").html('<i class="fas fa-paper-plane"></i>');
            $(".send_msg_btn").prop('disabled', false);
            $(".send_msg_btn").removeClass('file-selected');

            // Remove submitting class
            form.removeClass('submitting');

            // Show error message
            alert('Error sending message. Please try again.');
            console.error('Error:', error);
        }
    });
}

/**
 * Add message instantly to chat (before AJAX)
 */
function addInstantMessage(messageText) {
    // Generate unique ID for this message
    const messageId = 'msg_' + Date.now();

    // Get current time
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    // Check if we need to add a date separator
    const lastDateSeparator = $("#chat-body-container .chat_date_time:last p").text();

    let dateHtml = '';
    if (lastDateSeparator !== 'Today') {
        dateHtml = `<div class="chat_date_time">
            <p>Today</p>
        </div>`;
    }

    // Add message to chat with clock icon
    $("#chat-body-container").append(
        dateHtml +
        `<div class="msg msg_right" id="${messageId}">
            <div class="chat_bubble">
                <p class="">${escapeHtml(messageText)}</p>
                <span class="message-time">
                    ${timeString}
                    <i class="fas fa-clock" style="margin-left: 5px; color: #999;"></i>
                </span>
            </div>
        </div>`
    );

    window.checkMessageField();
    return messageId;
}

/**
 * Add message to chat container
 */
function addMessageToChat(messageData) {
    // Get message timestamp from server response
    const messageDate = new Date(messageData.created_at);

    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();
    const messageDay = messageDate.toDateString();

    // Format time
    const timeString = messageDate.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    // Determine date display
    let dateDisplay = '';
    if (messageDay === today) {
        dateDisplay = 'Today';
    } else if (messageDay === yesterday) {
        dateDisplay = 'Yesterday';
    } else {
        dateDisplay = messageDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    // Check if we need to add a date separator
    const lastDateSeparator = $("#chat-body-container .chat_date_time:last p").text();

    let dateHtml = '';
    if (lastDateSeparator !== dateDisplay) {
        dateHtml = `<div class="chat_date_time">
            <p>${dateDisplay}</p>
        </div>`;
    }

    // Generate message content based on type
    let messageContent = '';
    if (messageData.message_type === 'text') {
        messageContent = `<p class="">${escapeHtml(decodeHtml(messageData.message))}</p>`;
    } else if (messageData.message_type === 'image') {
        const attachment = messageData.attachment;
        messageContent = `
            <div class="message-attachment">
                <img src="${escapeHtml(attachment.file_url)}" alt="${escapeHtml(attachment.original_name)}"
                     style="max-width: 200px; max-height: 200px; border-radius: 8px; cursor: pointer;"
                     onclick="window.open('${escapeHtml(attachment.file_url)}', '_blank')">
                ${messageData.message && messageData.message !== 'Sent an image' ? `<p class="">${escapeHtml(decodeHtml(messageData.message))}</p>` : ''}
            </div>`;
    }

    $("#chat-body-container").append(
        dateHtml +
        `<div class="msg msg_right">
            <div class="chat_bubble">
                ${messageContent}
                <span class="message-time">${timeString}</span>
            </div>
        </div>`
    );

    window.checkMessageField();
}






/**
 * Handle image selection
 */
function handleImageSelection(file) {
    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
        alert('Image size must be less than 5MB');
        $("#image-input").val(''); // Clear the input
        return false;
    }

    // Show file info
    const fileName = file.name;
    const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

    // Update message input to show file info
    $("input[name='message']").attr('placeholder', `Image selected: ${fileName} (${fileSize} MB)`);

    // Show send button as active
    $(".send_msg_btn").addClass('file-selected');

    return true;
}

// =============================================
// MESSAGE SEARCH FUNCTIONS
// =============================================

/**
 * Search messages within current conversation
 */
function searchMessages(query) {
    if (!currentConversationIds || !query.trim()) {
        clearMessageSearch();
        return;
    }

    isSearching = true;
    currentSearchQuery = query.trim();

    // Show loading state
    $('.search_buttons_wrapper .search_above, .search_buttons_wrapper .search_below').prop('disabled', true);

    const searchUrl = routes.searchMessages.replace('CONVERSATION_ID', currentConversationIds);

    $.ajax({
        url: searchUrl,
        method: 'GET',
        data: {
            query: currentSearchQuery,
            page: 1
        },
        success: function(response) {
            if (response.status && response.data.messages.length > 0) {
                searchResults = response.data.messages;

                // Show search results in a different way - load the messages that contain the search term
                displaySearchResults(response.data);

                console.log(`🔍 Found ${searchResults.length} messages matching "${currentSearchQuery}"`);
            } else {
                searchResults = [];
                currentSearchIndex = -1;
                showNoResultsMessage();
                console.log(`🔍 No messages found matching "${currentSearchQuery}"`);
            }
        },
        error: function(xhr) {
            console.error('❌ Error searching messages:', xhr);
            searchResults = [];
            currentSearchIndex = -1;
            $('.search_buttons_wrapper .search_above, .search_buttons_wrapper .search_below').prop('disabled', true);
        },
        complete: function() {
            isSearching = false;
        }
    });
}

/**
 * Clear message search and remove highlights
 */
function clearMessageSearch() {
    searchResults = [];
    currentSearchIndex = -1;
    currentSearchQuery = '';
    isSearching = false;

    // Remove all search highlights and restore original text
    $('#chat-body-container .chat_bubble p').each(function() {
        const $this = $(this);
        if ($this.find('.search-highlight').length > 0) {
            // Get the original text without HTML tags
            const originalText = $this.text();
            $this.text(originalText);
        }
    });

    // Disable navigation buttons
    $('.search_buttons_wrapper .search_above, .search_buttons_wrapper .search_below').prop('disabled', true);

    console.log('🔍 Search cleared');
}

/**
 * Highlight search results in the chat
 */
function highlightSearchResults() {
    if (!currentSearchQuery || searchResults.length === 0) return;

    // Remove existing highlights
    $('#chat-body-container .search-highlight').removeClass('search-highlight current-search-result');

    // Create regex for case-insensitive search
    const regex = new RegExp(`(${escapeRegex(currentSearchQuery)})`, 'gi');

    // Find and highlight matching text in messages
    $('#chat-body-container .chat_bubble p').each(function() {
        const $this = $(this);
        const text = $this.text();

        if (regex.test(text)) {
            const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
            $this.html(highlightedText);
        }
    });
}

/**
 * Navigate to specific search result
 */
function navigateToSearchResult(index) {
    if (searchResults.length === 0 || index < 0 || index >= searchResults.length) return;

    currentSearchIndex = index;
    highlightCurrentSearchResult();

    console.log(`🔍 Navigated to search result ${index + 1} of ${searchResults.length}`);
}

/**
 * Navigate to next search result
 */
function navigateToNextSearchResult() {
    if (searchResults.length === 0) return;

    const nextIndex = (currentSearchIndex + 1) % searchResults.length;
    navigateToSearchResult(nextIndex);
}

/**
 * Navigate to previous search result
 */
function navigateToPreviousSearchResult() {
    if (searchResults.length === 0) return;

    const prevIndex = currentSearchIndex <= 0 ? searchResults.length - 1 : currentSearchIndex - 1;
    navigateToSearchResult(prevIndex);
}

/**
 * Show no results message
 */
function showNoResultsMessage() {
    // You can implement a toast notification or temporary message here
    console.log('🔍 No search results found');

    // Disable navigation buttons
    $('.search_buttons_wrapper .search_above, .search_buttons_wrapper .search_below').prop('disabled', true);
}

/**
 * Display search results by showing relevant messages
 */
function displaySearchResults(searchData) {
    const messages = searchData.messages;
    const query = searchData.query;

    if (messages.length === 0) {
        showNoResultsMessage();
        return;
    }

    // Clear current chat and show search results
    const chatContainer = $('#chat-body-container');
    chatContainer.html('');

    // Add search header
    chatContainer.append(`
        <div class="search-results-header" style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
            <h6 style="margin: 0; color: #666;">Search Results for "${escapeHtml(query)}"</h6>
            <small style="color: #999;">${messages.length} message${messages.length > 1 ? 's' : ''} found</small>
            <button class="btn btn-sm btn-outline-primary ms-2" onclick="exitSearchMode()" style="font-size: 12px;">
                <i class="fas fa-times"></i> Back to Chat
            </button>
        </div>
    `);

    // Display each search result
    messages.forEach((message, index) => {
        const isOwnMessage = message.is_own_message;
        const messageClass = isOwnMessage ? 'msg_right' : 'msg_left';
        const timeString = message.time_string;
        const highlightedContent = highlightSearchTerm(message.content, query);

        chatContainer.append(`
            <div class="msg ${messageClass} search-result-message" data-message-index="${index}">
                <div class="chat_bubble">
                    <p>${highlightedContent}</p>
                    <span class="message-time">${timeString}</span>
                    <small class="search-result-date" style="display: block; font-size: 10px; color: #999; margin-top: 3px;">
                        ${message.created_at_human}
                    </small>
                </div>
            </div>
        `);
    });

    // Enable navigation buttons
    $('.search_buttons_wrapper .search_above, .search_buttons_wrapper .search_below').prop('disabled', false);

    // Set up navigation
    currentSearchIndex = 0;
    highlightCurrentSearchResult();

    // Scroll to first result
    setTimeout(() => {
        scrollToBottom();
    }, 100);
}

/**
 * Highlight search term in text
 */
function highlightSearchTerm(text, query) {
    if (!query) return escapeHtml(text);

    const escapedText = escapeHtml(text);
    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
    return escapedText.replace(regex, '<span class="search-highlight">$1</span>');
}

/**
 * Highlight current search result
 */
function highlightCurrentSearchResult() {
    // Remove previous current highlight
    $('.current-search-result').removeClass('current-search-result');

    // Highlight current result
    const currentMessage = $(`.search-result-message[data-message-index="${currentSearchIndex}"]`);
    if (currentMessage.length) {
        currentMessage.find('.search-highlight').first().addClass('current-search-result');

        // Scroll to current result
        const chatContainer = $('.chat_body.scrollable-section-2');
        const messageTop = currentMessage.offset().top;
        const containerTop = chatContainer.offset().top;
        const containerHeight = chatContainer.height();
        const currentScroll = chatContainer.scrollTop();

        const targetScroll = currentScroll + (messageTop - containerTop) - (containerHeight / 2);

        chatContainer.animate({
            scrollTop: targetScroll
        }, 300);
    }
}

/**
 * Exit search mode and return to normal chat
 */
function exitSearchMode() {
    // Clear search
    $('#chat_search').val('');
    clearMessageSearch();

    // Hide search container
    $('.chat_inner_search').hide();

    // Reload the conversation messages
    if (currentConversationIds) {
        parseMessage(currentConversationIds);
    }
}

// Make exitSearchMode globally accessible
window.exitSearchMode = exitSearchMode;

/**
 * Escape regex special characters
 */
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// =============================================
// DATE MANAGEMENT FUNCTIONS
// =============================================

/**
 * Update date separators dynamically
 */
function updateDateSeparators() {
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();

    $("#chat-body-container .chat_date_time p").each(function() {
        const $this = $(this);
        const text = $this.text();

        // Skip if already showing relative dates
        if (text === 'Today' || text === 'Yesterday') {
            return;
        }

        // Try to parse the date
        const dateMatch = text.match(/(\w{3}) (\d{1,2}), (\d{4})/);
        if (dateMatch) {
            const dateStr = `${dateMatch[1]} ${dateMatch[2]}, ${dateMatch[3]}`;
            const messageDate = new Date(dateStr);
            const messageDateString = messageDate.toDateString();

            if (messageDateString === today) {
                $this.text('Today');
            } else if (messageDateString === yesterday) {
                $this.text('Yesterday');
            }
        }
    });
}

// =============================================
// EVENT HANDLERS
// =============================================

/**
 * Initialize all event handlers
 */
function initializeEventHandlers() {
    // Tab switching handler
    $(document).on("click",
        ".messenger_main_sec .messenger_wrapper .inner_section_messenger_sidebar .nav-pills .nav-item button",
        function() {
            setTimeout(() => {
                setDynamicHeight();
            }, 200);
        });

    // Search user handler
    $(document).on("keyup", "#search-user-input", function() {
        let name = $(this).val();
        $("#chat-threads-container").html(spinner);
        clearTimeout(window.debounceTimer);
        window.debounceTimer = setTimeout(function() {
            // Maintain current filter when searching
            getConversations(name, currentFilter !== 'All' ? currentFilter : null);
        }, 500);
    });

    // Load more conversations handler
    $(document).on("click", ".load-more-conversation", function() {
        $(this).remove();
        let url = $(this).attr("data-next-page");
        $.ajax({
            url: url,
            success: function(response) {
                if (response.status == true) {
                    $("#chat-threads-container").append(response.data);
                }
            }
        });
    });

    // Conversation click handler
    $(document).on("click", ".conversation-user", function(e) {
        e.preventDefault();
        e.stopPropagation();

        let conversation_ids = $(this).attr("data-conversation-ids");
        let receiver_id = $(this).attr("data-user-ids");

        console.log('🖱️ Conversation clicked:', conversation_ids, 'Current:', currentConversationIds);

        // Prevent duplicate API calls if this conversation is already active
        if (currentConversationIds === conversation_ids) {
            console.log('✋ Conversation already active, skipping API call');
            return;
        }

        // Ensure we have valid IDs
        if (!conversation_ids) {
            console.error('❌ No conversation ID found');
            return;
        }

        let url = routes.messageIndex.replace('CONVERSATION_ID', conversation_ids);
        window.history.pushState({}, '', url);
        activeConversation(conversation_ids, receiver_id);
    });

    // Message form submit handler
    $(document).on("submit", "#message-form", function(e) {
        e.preventDefault();
        handleMessageSend($(this));
    });

    // Handle Enter key press on message input
    $(document).on("keydown", "input[name='message']", function(e) {
        if (e.which === 13) {
            e.preventDefault();
            $("#message-form").trigger('submit');
        }
    });

    // Handle typing detection on message input
    $(document).on("input keydown keyup", "input[name='message']", function(e) {
        // Don't trigger typing on Enter key (message send)
        if (e.type === 'keydown' && e.which === 13) {
            // Stop typing when sending message
            stopTyping();
            return;
        }

        const messageText = $(this).val().trim();

        // Only trigger typing if there's actual content and user is actively typing
        if (messageText.length > 0 && (e.type === 'input' || e.type === 'keydown')) {
            startTyping();
            resetTypingTimer();
        } else if (messageText.length === 0) {
            // If input is empty, stop typing immediately
            stopTyping();
        }
    });

    // Handle typing stop when input loses focus
    $(document).on("blur", "input[name='message']", function() {
        stopTyping();
    });

    // Handle typing stop when user clicks away or switches tabs
    $(document).on("click", function(e) {
        // If click is not on the message input, stop typing
        if (!$(e.target).is("input[name='message']")) {
            stopTyping();
        }
    });

    // Handle typing stop when page loses focus (user switches tabs)
    $(window).on("blur", function() {
        stopTyping();
    });

    // Message search handlers
    $(document).on("input", "#chat_search", function() {
        const query = $(this).val().trim();

        // Clear search timer
        if (window.searchTimer) {
            clearTimeout(window.searchTimer);
        }

        if (query.length === 0) {
            clearMessageSearch();
            return;
        }

        // Debounce search to avoid too many requests
        window.searchTimer = setTimeout(() => {
            searchMessages(query);
        }, 500);
    });

    // Search navigation handlers
    $(document).on("click", ".search_above", function(e) {
        e.preventDefault();
        console.log('🔍 Previous button clicked', { isSearching, currentSearchIndex, searchResultsCount: searchResults.length });

        if (!isSearching && searchResults.length > 0) {
            navigateToPreviousSearchResult();
        }
    });

    $(document).on("click", ".search_below", function(e) {
        e.preventDefault();
        console.log('🔍 Next button clicked', { isSearching, currentSearchIndex, searchResultsCount: searchResults.length });

        if (!isSearching && searchResults.length > 0) {
            navigateToNextSearchResult();
        }
    });

    // Clear search when input is cleared
    $(document).on("keydown", "#chat_search", function(e) {
        if (e.key === 'Escape') {
            $(this).val('');
            clearMessageSearch();
        }
    });

    // Toggle search visibility
    $(document).on("click", ".find_messages_btn", function(e) {
        e.preventDefault();
        const searchContainer = $('.chat_inner_search');
        const searchInput = $('#chat_search');

        if (searchContainer.is(':visible')) {
            searchContainer.hide();
            clearMessageSearch();
            searchInput.val('');
        } else {
            searchContainer.show();
            searchInput.focus();
        }
    });

    // Image button click handler
    $(document).on("click", "#image-button", function(e) {
        e.preventDefault();
        $("#image-input").click();
    });

    // Image input change handler
    $(document).on("change", "#image-input", function() {
        const file = this.files[0];
        if (file) {
            handleImageSelection(file);
        }
    });

    // Load more messages handler (legacy)
    $(document).on("click", ".load-more-message", function() {
        let message_container = $(this).parent();
        $(this).remove();
        let url = $(this).attr("data-next-page");
        $.ajax({
            url: url,
            success: function(response) {
                if (response.status == true) {
                    message_container.prepend(response.data);
                }
            }
        });
    });

    // Window resize and scroll handlers
    $(window).on("resize", setDynamicHeight);
    $(window).on("scroll", setDynamicHeight);
}

// =============================================
// INITIALIZATION FUNCTIONS
// =============================================

/**
 * Initialize messenger system
 */
function initializeMessenger() {
    // Get configuration from window object
    if (window.messengerConfig) {
        auth_id = window.messengerConfig.auth_id;
        routes = window.messengerConfig.routes;
    } else {
        console.error('❌ Messenger configuration not found in window object');
        return;
    }

    console.log('🚀 Initializing messenger system...');
    console.log('👤 Auth ID:', auth_id);
    console.log('🛣️ Routes:', routes);

    // Initialize Pusher
    initializePusher();

    // Initialize event handlers
    initializeEventHandlers();

    // Set dynamic height
    setDynamicHeight();

    // Load conversations on page load
    getConversations();

    // Handle URL-based conversation loading (page reload)
    handleUrlConversation();

    // Handle active conversation from config (when redirected from "Contact Host")
    // Only if no conversation is already being loaded from URL
    const urlPath = window.location.pathname;
    const hasUrlConversation = urlPath.match(/\/message\/me\/([^\/]+)$/);

    if (window.messengerConfig.active_conversation_id && !hasUrlConversation) {
        console.log('🎯 Auto-opening conversation:', window.messengerConfig.active_conversation_id);
        setTimeout(() => {
            openConversationById(window.messengerConfig.active_conversation_id);
        }, 2000); // Wait longer for conversations to load first
    }

    // Update date separators every 10 minutes
    setInterval(updateDateSeparators, 600000);

    // Initial call to update date separators
    updateDateSeparators();

    console.log('✅ Messenger system initialized successfully');
}

/**
 * Open conversation by ID (used when redirected from "Contact Host")
 */
function openConversationById(conversation_ids) {
    console.log('🎯 Opening conversation by ID:', conversation_ids);

    // Prevent duplicate calls
    if (isInitializingConversation && currentConversationIds === conversation_ids) {
        console.log('⚠️ Conversation already being opened, skipping duplicate call');
        return;
    }

    // Find the conversation element
    const conversationElement = $(`.conversation-user[data-conversation-ids="${conversation_ids}"]`);

    if (conversationElement.length) {
        // Get receiver ID from the element
        const receiver_id = conversationElement.attr("data-user-ids");

        // Update URL
        let url = routes.messageIndex.replace('CONVERSATION_ID', conversation_ids);
        window.history.pushState({}, '', url);

        // Activate the conversation
        activeConversation(conversation_ids, receiver_id);
    } else {
        console.log('⚠️ Conversation not found in list, it may still be loading:', conversation_ids);
        // Try again after a short delay (but limit retries)
        if (!window.openConversationRetries) window.openConversationRetries = 0;
        if (window.openConversationRetries < 5) {
            window.openConversationRetries++;
            setTimeout(() => {
                openConversationById(conversation_ids);
            }, 500);
        } else {
            console.log('❌ Max retries reached for opening conversation:', conversation_ids);
            window.openConversationRetries = 0;
        }
    }
}

/**
 * Handle conversation loading from URL (page reload)
 */
function handleUrlConversation() {
    const urlPath = window.location.pathname;
    const conversationMatch = urlPath.match(/\/message\/me\/([^\/]+)$/);

    if (conversationMatch) {
        const conversation_ids = conversationMatch[1];
        console.log('🔄 Page reload detected, opening conversation:', conversation_ids);

        // Prevent duplicate initialization
        if (isInitializingConversation && currentConversationIds === conversation_ids) {
            console.log('⚠️ Conversation already being initialized from URL, skipping');
            return;
        }

        // Set current conversation
        currentConversationIds = conversation_ids;

        // After conversations are loaded (from initialization), open the specific conversation
        setTimeout(() => {
            // Find and activate the conversation
            const conversationElement = $(`.conversation-user[data-conversation-ids="${conversation_ids}"]`);
            if (conversationElement.length) {
                conversationElement.addClass("active-conversation").siblings().removeClass("active-conversation");

                // Only call getUserConversation if not already initializing
                if (!isInitializingConversation) {
                    getUserConversation(conversation_ids);
                }
            } else {
                console.log('⚠️ Conversation not found in list:', conversation_ids);
            }
        }, 1500);
    }
}

// =============================================
// TYPING INDICATOR DEBUG FUNCTIONS
// =============================================

/**
 * Debug function to test typing indicator manually
 */
window.testTypingIndicator = function(userName = 'Test User', show = true) {
    if (show) {
        showTypingIndicator(userName);
        console.log(`🧪 Manual test: Showing typing indicator for ${userName}`);
    } else {
        hideTypingIndicator(userName);
        console.log(`🧪 Manual test: Hiding typing indicator for ${userName}`);
    }
};

/**
 * Debug function to test typing status sending
 */
window.testTypingStatus = function(typing = true) {
    if (!currentConversationIds) {
        console.log('❌ No active conversation for typing test');
        return;
    }

    sendTypingStatus(typing);
    console.log(`🧪 Manual test: Sent typing status ${typing} for conversation ${currentConversationIds}`);
};

/**
 * Debug function to show current typing state
 */
window.getTypingState = function() {
    return {
        currentConversationIds: currentConversationIds,
        isTyping: isTyping,
        typingUsers: Array.from(typingUsers),
        typingIndicatorVisible: $('#typing-indicator-container').is(':visible'),
        typingTimer: typingTimer !== null,
        containerExists: $('#typing-indicator-container').length > 0
    };
};

/**
 * Debug function to force clear all typing indicators
 */
window.clearAllTyping = function() {
    console.log('🧪 Force clearing all typing indicators');

    // Clear our own typing
    stopTyping();

    // Clear all typing users
    typingUsers.clear();

    // Update display
    updateTypingIndicatorDisplay();

    console.log('🧪 All typing indicators cleared');
};

// =============================================
// DEBUG FUNCTIONS
// =============================================

/**
 * Debug function to manually test load more messages
 */
window.testLoadMore = function(page = null) {
    if (currentConversationIds) {
        const nextPage = page || parseInt($(".load-more-messages-indicator").attr('data-next-page')) || 2;
        console.log('🧪 Manual test: Loading page', nextPage);

        // Reset loading flag and force load
        isLoadingMoreMessages = false;
        loadMoreMessages(currentConversationIds, nextPage);
    } else {
        console.log('❌ No active conversation for test');
    }
};

/**
 * Debug function to test complete load more flow
 */
window.testCompleteLoadMore = function(page = 2) {
    if (!currentConversationIds) {
        console.log('❌ No active conversation');
        return;
    }

    console.log('🧪 Testing complete load more flow...');

    // Step 1: Check current state
    console.log('📍 Step 1: Current state');
    const currentState = checkLoadMoreState();

    // Step 2: Test API
    console.log('📍 Step 2: Testing API');
    const url = routes.loadMoreMessages.replace('CONVERSATION_ID', currentConversationIds);

    $.ajax({
        url: url,
        data: {
            page: page,
            per_page: 20
        },
        success: function(response) {
            console.log('📍 Step 3: API Response received');
            console.log('📥 Response:', response);

            // Step 4: Manually update pagination
            console.log('📍 Step 4: Updating pagination manually');
            updatePaginationInfo(response.pagination);

            // Step 5: Check state after update
            setTimeout(() => {
                console.log('📍 Step 5: State after manual update');
                const newState = checkLoadMoreState();

                // Step 6: Test scroll trigger
                console.log('📍 Step 6: Testing scroll trigger');
                if (newState.hasMore) {
                    const chatContainer = $(".chat_body.scrollable-section-2");
                    chatContainer.scrollTop(0);
                    console.log('✅ Scrolled to top - should trigger load more now');
                } else {
                    console.log('❌ Still showing no more messages after update');
                }
            }, 200);
        },
        error: function(xhr) {
            console.log('❌ API Error:', xhr);
        }
    });
};

/**
 * Debug function to test API response directly
 */
window.testApiResponse = function(page = 2) {
    if (!currentConversationIds) {
        console.log('❌ No active conversation');
        return;
    }

    const url = routes.loadMoreMessages.replace('CONVERSATION_ID', currentConversationIds);
    console.log('🧪 Testing API directly:', url);

    $.ajax({
        url: url,
        data: {
            page: page,
            per_page: 20
        },
        success: function(response) {
            console.log('📥 API Response:', response);
            console.log('📊 Pagination from API:', response.pagination);

            if (response.pagination) {
                console.log(`📍 Current Page: ${response.pagination.current_page}`);
                console.log(`📍 Last Page: ${response.pagination.last_page}`);
                console.log(`📍 Total Messages: ${response.pagination.total}`);
                console.log(`📍 Has More Pages: ${response.pagination.has_more_pages}`);
                console.log(`📍 Per Page: ${response.pagination.per_page}`);
            }
        },
        error: function(xhr) {
            console.log('❌ API Error:', xhr);
        }
    });
};

/**
 * Debug function to reset and test load more functionality
 */
window.resetAndTestLoadMore = function() {
    console.log('🔄 Resetting and testing load more functionality...');

    if (!currentConversationIds) {
        console.log('❌ No active conversation');
        return;
    }

    // Reset loading state
    isLoadingMoreMessages = false;

    // Force set indicator to have more pages for testing
    const indicator = $(".load-more-messages-indicator");
    indicator.attr('data-has-more', 'true');
    indicator.attr('data-next-page', '2');

    console.log('✅ Reset indicator state - has_more: true, next_page: 2');

    // Test scroll detection
    const chatContainer = $(".chat_body.scrollable-section-2");
    if (chatContainer.length) {
        chatContainer.scrollTop(0);
        console.log('✅ Scrolled to top');

        // Manually trigger the scroll event
        setTimeout(() => {
            chatContainer.trigger('scroll');
            console.log('✅ Triggered scroll event');
        }, 100);
    } else {
        console.log('❌ Chat container not found');
    }
};

/**
 * Debug function to force scroll to top and trigger load more
 */
window.forceLoadMore = function() {
    if (currentConversationIds) {
        const chatContainer = $(".chat_body.scrollable-section-2");
        const hasMore = $(".load-more-messages-indicator").attr('data-has-more') === 'true';
        const nextPage = parseInt($(".load-more-messages-indicator").attr('data-next-page')) || 2;

        console.log('🧪 Force Load More Test:');
        console.log(`📍 Has More: ${hasMore}`);
        console.log(`📍 Next Page: ${nextPage}`);
        console.log(`📍 Loading: ${isLoadingMoreMessages}`);
        console.log(`📍 Conversation: ${currentConversationIds}`);

        if (hasMore && !isLoadingMoreMessages) {
            // Scroll to top first
            chatContainer.scrollTop(0);

            // Force trigger load more
            isLoadingMoreMessages = true;
            console.log('🔄 Force triggering load more...');
            loadMoreMessages(currentConversationIds, nextPage);
        } else {
            console.log('❌ Cannot load more - hasMore:', hasMore, 'isLoading:', isLoadingMoreMessages);
        }
    } else {
        console.log('❌ No active conversation for test');
    }
};

/**
 * Debug function to manually test header badge update
 */
window.testHeaderBadge = function(count) {
    console.log('🧪 Manual test: Updating header badge to', count);
    if (window.updateHeaderBadge) {
        window.updateHeaderBadge(count);
    } else {
        console.log('❌ updateHeaderBadge function not available');
    }
};

/**
 * Debug function to test scroll detection
 */
window.testScrollDetection = function() {
    const chatContainer = $(".chat_body.scrollable-section-2");
    if (chatContainer.length) {
        const scrollTop = chatContainer.scrollTop();
        const scrollHeight = chatContainer[0].scrollHeight;
        const clientHeight = chatContainer[0].clientHeight;
        const hasMore = $(".load-more-messages-indicator").attr('data-has-more') === 'true';
        const nextPage = parseInt($(".load-more-messages-indicator").attr('data-next-page')) || 2;

        console.log('🧪 Scroll Detection Test:');
        console.log(`📏 ScrollTop: ${scrollTop}`);
        console.log(`📏 ScrollHeight: ${scrollHeight}`);
        console.log(`📏 ClientHeight: ${clientHeight}`);
        console.log(`📍 Has More: ${hasMore}`);
        console.log(`📍 Next Page: ${nextPage}`);
        console.log(`📍 Loading: ${isLoadingMoreMessages}`);
        console.log(`📍 Conversation: ${currentConversationIds}`);
        console.log(`📍 Load Indicator: ${$(".load-more-messages-indicator").length}`);

        // Test scroll to top
        chatContainer.scrollTop(0);
        console.log('📜 Scrolled to top for testing');
    } else {
        console.log('❌ Chat container not found');
    }
};

/**
 * Debug function to force enable load more for testing
 */
window.forceEnableLoadMore = function(nextPage = 2) {
    const indicator = $(".load-more-messages-indicator");
    indicator.attr('data-has-more', 'true');
    indicator.attr('data-next-page', nextPage);

    // Reset loading flag
    isLoadingMoreMessages = false;

    console.log(`✅ Force enabled load more - next page: ${nextPage}`);
    console.log('🔄 Now scroll to top to test...');

    // Scroll to top to trigger
    const chatContainer = $(".chat_body.scrollable-section-2");
    if (chatContainer.length) {
        chatContainer.scrollTop(0);
    }

    return checkLoadMoreState();
};

/**
 * Debug function to check load more indicator state
 */
window.checkLoadMoreState = function() {
    const indicator = $(".load-more-messages-indicator");
    const hasMore = indicator.attr('data-has-more');
    const nextPage = indicator.attr('data-next-page');

    console.log('🔍 Load More Indicator State:');
    console.log(`📍 Indicator found: ${indicator.length}`);
    console.log(`📍 Has More: ${hasMore}`);
    console.log(`📍 Next Page: ${nextPage}`);
    console.log(`📍 Currently Loading: ${isLoadingMoreMessages}`);
    console.log(`📍 Current Conversation: ${currentConversationIds}`);
    console.log(`📍 Indicator HTML:`, indicator.html());

    return {
        found: indicator.length > 0,
        hasMore: hasMore === 'true',
        nextPage: parseInt(nextPage) || 0,
        isLoading: isLoadingMoreMessages,
        conversationId: currentConversationIds
    };
};

/**
 * Debug function to check message count in UI
 */
window.checkMessageCount = function() {
    const messages = $("#chat-body-container .msg").length;
    const datesSeparators = $("#chat-body-container .chat_date_time").length;
    const totalElements = $("#chat-body-container > *").length;

    console.log('📊 Message Count Check:');
    console.log(`💬 Messages: ${messages}`);
    console.log(`📅 Date Separators: ${datesSeparators}`);
    console.log(`📦 Total Elements: ${totalElements}`);
    console.log(`🔄 Current Conversation: ${currentConversationIds}`);

    // Show first and last message dates
    const firstMsg = $("#chat-body-container .msg:first");
    const lastMsg = $("#chat-body-container .msg:last");

    if (firstMsg.length) {
        console.log(`📅 First Message Time: ${firstMsg.find('.message-time').text()}`);
    }
    if (lastMsg.length) {
        console.log(`📅 Last Message Time: ${lastMsg.find('.message-time').text()}`);
    }
};

// =============================================
// DOCUMENT READY
// =============================================

$(document).ready(function() {
    // Initialize the messenger system
    initializeMessenger();

    // $('.find_messages_btn').on('click', function () {
    $(document).on('click', '.find_messages_btn', function () {
        const $button = $(this);
        const $searchBox = $('.chat_inner_search');

        const offset = $button.offset();
        const buttonHeight = $button.outerHeight();

        console.log(offset.top);
        console.log(buttonHeight);

        const top = offset.top - 10;

        $searchBox
            .css({
                // top: top + 'px',
                // left: offset.left + 'px',
                // right: '0px',
                display: 'block'
            })
            .delay(10)
            .queue(function (next) {
                $(this).toggleClass('show');
                next();
            });
    });

    $(document).on('click', '.filter-option', function (e) {
        e.preventDefault();

        // Toggle active class
        $('.filter-option').removeClass('active');
        $(this).addClass('active');

        // Get selected filter
        var selectedFilter = $(this).data('filter');
        var selectedFilterImage = $(this).data('image');
        console.log("Selected filter:", selectedFilter);
        $('#pills-conversations-tab').html('<span><img src="'+  selectedFilterImage + '" alt="" /></span><span>' + selectedFilter + '</span>');

        // Apply filter to conversations
        applyConversationFilter(selectedFilter);
    });

    // Function to apply conversation filter
    function applyConversationFilter(filter) {
        // Update current filter
        currentFilter = filter;

        // Show loading state
        $('#chat-threads-container').html('<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');

        // Use the existing getConversations function with filter parameter
        getConversations(null, filter);
    }


    $(document).on('keyup','#message-form .typing_attchment_wrapper input[type="text"]',function(){
        window.checkMessageField();
    });

    window.checkMessageField = function () {
        var charCount = $('#message-form .typing_attchment_wrapper input[type="text"]').val();
        if(charCount == ''){
            $('#message-form .send_button .send_msg_btn').prop('disabled', true);
        }else {
            $('#message-form .send_button .send_msg_btn').prop('disabled', false);
        }
    }

});

    // =============================================
    // EMOJI PICKER FUNCTIONALITY
    // =============================================

    // Emoji data organized by categories
    const emojiData = {
        smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'],
        people: ['👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃', '🧠', '🦷', '🦴', '👀', '👁️', '👅', '👄', '💋', '🩸'],
        nature: ['🌸', '💮', '🏵️', '🌹', '🥀', '🌺', '🌻', '🌼', '🌷', '🌱', '🪴', '🌲', '🌳', '🌴', '🌵', '🌶️', '🍄', '🌾', '💐', '🌿', '🍀', '🍃', '🍂', '🍁', '🌊', '🌀', '🌈', '🌂', '☂️', '☔', '⛱️', '⚡', '❄️', '☃️', '⛄', '☄️', '🔥', '💧', '🌟', '⭐', '🌠', '☀️', '🌤️', '⛅', '🌦️', '🌧️', '⛈️', '🌩️', '🌨️', '🌙', '🌛', '🌜', '🌚', '🌕', '🌖', '🌗', '🌘', '🌑', '🌒', '🌓', '🌔'],
        food: ['🍎', '🍏', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈', '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦', '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔', '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈', '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟', '🍕', '🫓', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕', '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙', '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦', '🥧', '🧁', '🍰', '🎂', '🍮', '🍭', '🍬', '🍫', '🍿', '🍩', '🍪', '🌰', '🥜', '🍯'],
        activities: ['⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️', '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️‍♀️', '🏋️', '🏋️‍♂️', '🤼‍♀️', '🤼', '🤼‍♂️', '🤸‍♀️', '🤸', '🤸‍♂️', '⛹️‍♀️', '⛹️', '⛹️‍♂️', '🤺', '🤾‍♀️', '🤾', '🤾‍♂️', '🏌️‍♀️', '🏌️', '🏌️‍♂️', '🏇', '🧘‍♀️', '🧘', '🧘‍♂️', '🏄‍♀️', '🏄', '🏄‍♂️', '🏊‍♀️', '🏊', '🏊‍♂️', '🤽‍♀️', '🤽', '🤽‍♂️', '🚣‍♀️', '🚣', '🚣‍♂️', '🧗‍♀️', '🧗', '🧗‍♂️', '🚵‍♀️', '🚵', '🚵‍♂️', '🚴‍♀️', '🚴', '🚴‍♂️'],
        travel: ['🚗', '🚕', '🚙', '🚌', '🚎', '🏎️', '🚓', '🚑', '🚒', '🚐', '🛻', '🚚', '🚛', '🚜', '🏍️', '🛵', '🚲', '🛴', '🛹', '🛼', '🚁', '🛸', '✈️', '🛩️', '🛫', '🛬', '🪂', '💺', '🚀', '🛰️', '🚉', '🚊', '🚝', '🚞', '🚋', '🚃', '🚋', '🚞', '🚝', '🚄', '🚅', '🚈', '🚂', '🚆', '🚇', '🚊', '🚉', '✈️', '🛫', '🛬', '🛩️', '💺', '🚁', '🚟', '🚠', '🚡', '🛰️', '🚀', '🛸', '🚢', '⛵', '🚤', '🛥️', '🛳️', '⛴️', '🚨', '🚥', '🚦', '🛑', '🚧'],
        objects: ['💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴', '💶', '💷', '🪙', '💰', '💳', '💎', '⚖️', '🪜', '🧰', '🔧', '🔨', '⚒️', '🛠️', '⛏️', '🪓', '🪚', '🔩', '⚙️', '🪤', '🧱', '⛓️', '🧲', '🔫', '💣', '🧨', '🪓', '🔪', '🗡️', '⚔️', '🛡️', '🚬', '⚰️', '🪦', '⚱️', '🏺', '🔮', '📿', '🧿', '💈', '⚗️', '🔭', '🔬', '🕳️', '🩹', '🩺', '💊', '💉', '🩸', '🧬', '🦠', '🧫', '🧪', '🌡️', '🧹', '🪣', '🧽', '🧴', '🛎️', '🔑', '🗝️', '🚪', '🪑', '🛋️', '🛏️', '🛌', '🧸', '🪆', '🖼️', '🪞', '🪟', '🛍️', '🛒', '🎁', '🎈', '🎏', '🎀', '🪄', '🪅', '🎊', '🎉', '🎎', '🏮', '🎐', '🧧'],
        symbols: ['❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️', '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐', '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐', '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '📴', '📳', '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️', '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️', '🅱️', '🆎', '🆑', '🅾️', '🆘', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '💢', '♨️', '🚷', '🚯', '🚳', '🚱', '🔞', '📵', '🚭', '❗', '❕', '❓', '❔', '‼️', '⁉️', '🔅', '🔆', '〽️', '⚠️', '🚸', '🔱', '⚜️', '🔰', '♻️', '✅', '🈯', '💹', '❇️', '✳️', '❎', '🌐', '💠']
    };

    let currentEmojiCategory = 'smileys';

    // Initialize emoji picker
    function initializeEmojiPicker() {
        populateEmojiGrid(currentEmojiCategory);
    }

    // Populate emoji grid with emojis from selected category
    function populateEmojiGrid(category) {
        const emojiGrid = document.getElementById('emoji-grid');
        emojiGrid.innerHTML = '';

        const emojis = emojiData[category] || emojiData.smileys;

        emojis.forEach(emoji => {
            const emojiItem = document.createElement('div');
            emojiItem.className = 'emoji-item';
            emojiItem.textContent = emoji;
            emojiItem.addEventListener('click', () => insertEmoji(emoji));
            emojiGrid.appendChild(emojiItem);
        });
    }

    // Insert emoji into message input
    function insertEmoji(emoji) {
        const messageInput = document.querySelector('#message-form input[name="message"]');
        if (messageInput) {
            const start = messageInput.selectionStart;
            const end = messageInput.selectionEnd;
            const text = messageInput.value;
            const before = text.substring(0, start);
            const after = text.substring(end, text.length);

            messageInput.value = before + emoji + after;
            messageInput.selectionStart = messageInput.selectionEnd = start + emoji.length;
            messageInput.focus();

            // Trigger input event to update send button state
            window.checkMessageField();
        }

        // Hide emoji picker after selection
        hideEmojiPicker();
    }

    // Show emoji picker
    function showEmojiPicker() {
        const emojiPicker = document.getElementById('emoji-picker');
        emojiPicker.classList.add('show');
        document.getElementById('emoji-button').classList.add('active');
    }

    // Hide emoji picker
    function hideEmojiPicker() {
        const emojiPicker = document.getElementById('emoji-picker');
        emojiPicker.classList.remove('show');
        document.getElementById('emoji-button').classList.remove('active');
    }

    // Toggle emoji picker
    function toggleEmojiPicker() {
        const emojiPicker = document.getElementById('emoji-picker');
        if (emojiPicker.classList.contains('show')) {
            hideEmojiPicker();
        } else {
            showEmojiPicker();
        }
    }

    // Emoji button click handler
    $(document).on('click', '#emoji-button', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleEmojiPicker();
    });

    // Emoji category click handler
    $(document).on('click', '.emoji-category', function(e) {
        e.preventDefault();

        // Update active category
        $('.emoji-category').removeClass('active');
        $(this).addClass('active');

        // Get selected category
        const category = $(this).data('category');
        currentEmojiCategory = category;

        // Populate grid with new category
        populateEmojiGrid(category);
    });

    // Close emoji picker when clicking outside
    $(document).on('click', function(e) {
        const emojiPicker = document.getElementById('emoji-picker');
        const emojiButton = document.getElementById('emoji-button');

        if (emojiPicker && emojiButton &&
            !emojiPicker.contains(e.target) &&
            !emojiButton.contains(e.target)) {
            hideEmojiPicker();
        }
    });

    // Handle keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Escape key to close emoji picker
        if (e.key === 'Escape') {
            hideEmojiPicker();
        }

        // Ctrl/Cmd + ; to toggle emoji picker
        if ((e.ctrlKey || e.metaKey) && e.key === ';') {
            e.preventDefault();
            toggleEmojiPicker();
        }
    });

    // Handle message input focus to hide emoji picker
    $(document).on('focus', '#message-form input[name="message"]', function() {
        // Don't hide immediately, add small delay to allow emoji selection
        setTimeout(() => {
            if (!document.getElementById('emoji-picker').matches(':hover')) {
                // hideEmojiPicker();
            }
        }, 100);
    });

    // Emoji search functionality
    function searchEmojis(query) {
        const emojiGrid = document.getElementById('emoji-grid');
        emojiGrid.innerHTML = '';

        if (!query.trim()) {
            populateEmojiGrid(currentEmojiCategory);
            return;
        }

        // Search through all emojis
        const allEmojis = Object.values(emojiData).flat();

        // For a more sophisticated search, you could add emoji names/keywords
        // For now, we'll show a subset of popular emojis when searching
        const popularEmojis = [
            '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '😊', '😍', '🥰', '😘', '😗', '😙', '😋',
            '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '👏', '🙌', '👐', '🤝', '🙏', '❤️', '🧡', '💛', '💚',
            '💙', '💜', '🖤', '🤍', '💔', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '🔥', '💯', '💢', '💥',
            '🎉', '🎊', '🎈', '🎁', '🏆', '🥇', '🥈', '🥉', '⭐', '🌟', '💫', '✨', '🌈', '☀️', '🌙', '⚡'
        ];

        // Filter emojis based on query (simple contains check)
        const filteredEmojis = popularEmojis.filter(emoji => {
            // You could implement more sophisticated matching here
            return true; // For now, show all popular emojis
        });

        filteredEmojis.forEach(emoji => {
            const emojiItem = document.createElement('div');
            emojiItem.className = 'emoji-item';
            emojiItem.textContent = emoji;
            emojiItem.addEventListener('click', () => insertEmoji(emoji));
            emojiGrid.appendChild(emojiItem);
        });

        // If no results, show a message
        if (filteredEmojis.length === 0) {
            const noResults = document.createElement('div');
            noResults.style.textAlign = 'center';
            noResults.style.padding = '20px';
            noResults.style.color = '#666';
            noResults.textContent = 'No emojis found';
            emojiGrid.appendChild(noResults);
        }
    }

    // Emoji search input handler
    $(document).on('input', '#emoji-search', function() {
        const query = $(this).val();
        searchEmojis(query);

        // Clear category selection when searching
        if (query.trim()) {
            $('.emoji-category').removeClass('active');
        } else {
            $('.emoji-category[data-category="' + currentEmojiCategory + '"]').addClass('active');
        }
    });

    // Clear search when category is selected
    $(document).on('click', '.emoji-category', function() {
        $('#emoji-search').val('');
    });

    // Initialize emoji picker when document is ready
    $(document).ready(function() {
        initializeEmojiPicker();
    });