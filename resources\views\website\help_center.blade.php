@extends('website.layout.master')
@push('css')
    <style>
        /* .card {
            border: 4px solid #00000030;
            border-radius: 10px;
            cursor: pointer;
        }

        .list-view .card {
            margin-bottom: 1rem;
        }

        .box-view .card {
            margin-bottom: 1rem;
            height: 300px;
        }

        .card:hover {
            box-shadow: 1px 1px 11px #ffc10791;
            border: 3px solid #ffc107
        }

        .box-view .card-body {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        } */
    </style>
@endpush
@section('content')
    <section class="faq py-5 help_center">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="search_parent w-100">
                            <form action="{{ route('help_center', ["locale" => app()->getLocale()]) }}" method="GET" class="search d-flex ms-3 pe-3 py-1">
                                <input type="text" id="search-listing-inp" name="search" value="{{ request()->search }}"
                                    class="form-control" placeholder="{{ translate('help_center.search_for_answer') }}">
                                <button class="btn"><i class="fa fa-search dark-yellow"></i></button>
                            </form>
                        </div>
                        <a href="{{ route('help_center', ["locale" => app()->getLocale()]) }}" class="reset_btn btn_black button">{{ translate('help_center.reset') }}</a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="about-box2 pt-4">
                        <h2 class=" ms-3" data-aos="fade">{{ translate('help_center.help_center') }}</h2>
                        @if(request()->search)
                            <p class="ms-3" data-aos="fade-up" data-aos-delay="200">
                                <strong>Search results for:</strong> "{{ request()->search }}"
                                ({{ $help_centers->total() }} {{ $help_centers->total() == 1 ? 'result' : 'results' }} found)
                            </p>
                        @else
                            <p class="ms-3" data-aos="fade-up" data-aos-delay="200">{{ translate('help_center.get_started') }}</p>
                        @endif
                    </div>
                    <div>
                        <div class="row">
                            <div class="col-12 my-4">
                                @empty($searchTerm)
                                    <!-- Toggle View Buttons -->
                                    <div class="d-flex justify-content-end mb-5 gap-3 view_btn_parent">
                                        <button id="list-view" class="view_btn">
                                            <i class="fas fa-list mx-2"></i> {{ translate('help_center.list_view') }}
                                        </button>
                                        <button id="box-view" class="view_btn" disabled>
                                            <i class="fas fa-th-large mx-2"></i> {{ translate('help_center.box_view') }}
                                        </button>
                                    </div>
                                @endempty

                                <!-- List/Box View Container -->
                                <div id="view-container" class="row box_parent">
                                    @forelse ($help_centers ?? [] as $help_center)
                                        <!-- Example Item 1 -->
                                        <div class="col-12 col-md-4 box-view-item">
                                            {{--  --}}
                                            <a href="{{ route("help_center_detail", ["locale"=> app()->getLocale(), "slug" => $help_center->slug]) }}"
                                                style="all:unset">
                                                <div class="card mb-4 b_shadow" data-aos="fade" data-aos-delay="300">
                                                    <div class="card-body w-100">
                                                        <h5 class="card-title semi-bold">{{ $help_center->title }}</h5>

                                                        </hr>

                                                        <p class="card-text">{!! $help_center->description !!}</p>

                                                        <p class="card-text yellow light-bold total_articles @if(request()->search) filtered_total_articles @endif">{{ translate('help_center.total_articles') }}
                                                            {{ count($help_center->faqs) }}</p>

                                                        <p class="card-text small last_update text-black-50">{{ translate('help_center.last_updated') }}
                                                            {{ $help_center->updated_at->diffForHumans() }}</p>

                                                            @if(request()->search)

                                                                <div class="searched_faq_wrapper" style="display: none;">

                                                                    @foreach ($help_center->faqs as $faq)
                                                                        <div class="single_filtered_faq">
                                                                            <a href="{{ route('faq_detail', ["slug" => $faq->slug, "locale" => app()->getLocale()]) }}" style="text-decoration: none">
                                                                                <h6 class="faq_title">{{ $faq->title }}</h6>
                                                                                <p class="faq_description">{{ strip_tags(preg_replace('/\s+/', ' ', $faq->description)) }}</p>
                                                                            </a>
                                                                        </div>
                                                                    @endforeach
                                                                    
                                                                </div>

                                                            @endif
                                                    </div>
                                                </div>
                                            </a>
                                        </div>
                                    @empty
                                        <div class="col-md-12" data-aos="fade" data-aos-delay="100">
                                            {{ translate('help_center.no_help_topics_found') }}
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            // Initially set box view
            @empty($searchTerm)
                $('#view-container').addClass('box-view');
            @endempty

            // Toggle to list view
            $('#list-view').click(function() {
                // Remove box-view and add list-view
                $('#view-container').removeClass('box-view').addClass('list-view');

                // Change child items to col-12 (full width)
                $('#view-container .box-view-item').removeClass('col-12 col-md-4').addClass('col-12');

                $('.searched_faq_wrapper').show();

                // Disable the list view button and enable the box view button
                $('#list-view').prop('disabled', true);
                $('#box-view').prop('disabled', false);

                $('.filtered_total_articles.total_articles').hide();

            });

            function isSearch(){
                $('#view-container').removeClass('box-view').addClass('list-view');
                $('#view-container .box-view-item').removeClass('col-12 col-md-4').addClass('col-12');
                $('.searched_faq_wrapper').show();
                $('#list-view').prop('disabled', true);
                $('#box-view').prop('disabled', false);
                $('.filtered_total_articles.total_articles').hide();
            }

            @if($searchTerm)
                isSearch();
            @endif

            // Toggle to box view
            $('#box-view').click(function() {
                // Remove list-view and add box-view
                $('#view-container').removeClass('list-view').addClass('box-view');

                // Change child items to col-4 (3 items per row)
                $('#view-container .box-view-item').removeClass('col-12').addClass('col-12 col-md-4');

                $('.searched_faq_wrapper').hide();

                // Disable the box view button and enable the list view button
                $('#box-view').prop('disabled', true);
                $('#list-view').prop('disabled', false);

                $('.filtered_total_articles.total_articles').show();
            });
        });
    </script>
@endpush
