<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typing Indicator Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .chat-simulation {
            height: 300px;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 10px;
            overflow-y: auto;
            background: #f9f9f9;
            position: relative;
        }
        
        /* Copy typing indicator styles from messenger CSS */
        .typing-indicator-container {
            padding: 10px 20px;
            background: transparent;
            border-top: 1px solid #e0e0e0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            background: #f5f5f5;
            border-radius: 20px;
            max-width: fit-content;
            animation: fadeIn 0.3s ease-in-out;
        }

        .typing-dots {
            display: flex;
            gap: 3px;
            align-items: center;
        }

        .typing-dots span {
            width: 6px;
            height: 6px;
            background: #999;
            border-radius: 50%;
            animation: typingDot 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        .typing-text {
            font-size: 12px;
            color: #666;
            font-style: italic;
        }

        @keyframes typingDot {
            0%, 60%, 100% {
                transform: scale(1);
                opacity: 0.5;
            }
            30% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            margin: 5px;
            padding: 10px 15px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Typing Indicator Test</h1>
    
    <div class="test-container">
        <h2>Visual Test</h2>
        <p>This tests the visual appearance and animation of the typing indicator.</p>
        
        <div class="chat-simulation" id="chat-simulation">
            <p>Chat messages would appear here...</p>
            
            <div id="typing-indicator-container" class="typing-indicator-container" style="display: none;">
                <!-- Typing indicator will be inserted here -->
            </div>
        </div>
        
        <div class="controls">
            <button onclick="showTyping('John Doe')">Show John Typing</button>
            <button onclick="showTyping('Jane Smith')">Show Jane Typing</button>
            <button onclick="showMultipleTyping()">Show Multiple Users Typing</button>
            <button onclick="hideTyping()">Hide Typing</button>
        </div>
        
        <div class="status" id="status">
            Status: Ready for testing
        </div>
    </div>
    
    <div class="test-container">
        <h2>Implementation Checklist</h2>
        <ul>
            <li>✅ Backend API endpoint for typing events</li>
            <li>✅ Frontend JavaScript for detecting typing</li>
            <li>✅ Pusher integration for real-time events</li>
            <li>✅ CSS animations for typing indicator</li>
            <li>✅ HTML container for typing indicator</li>
        </ul>
    </div>
    
    <script>
        let typingUsers = new Set();
        
        function updateTypingIndicatorDisplay() {
            const typingContainer = document.getElementById('typing-indicator-container');
            
            if (typingUsers.size === 0) {
                typingContainer.style.display = 'none';
                updateStatus('No one is typing');
                return;
            }

            let typingText = '';
            const usersArray = Array.from(typingUsers);
            
            if (usersArray.length === 1) {
                typingText = `${usersArray[0]} is typing...`;
            } else if (usersArray.length === 2) {
                typingText = `${usersArray[0]} and ${usersArray[1]} are typing...`;
            } else {
                typingText = `${usersArray[0]} and ${usersArray.length - 1} others are typing...`;
            }

            typingContainer.innerHTML = `
                <div class="typing-indicator">
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <span class="typing-text">${typingText}</span>
                </div>
            `;
            typingContainer.style.display = 'block';
            updateStatus(`Showing: ${typingText}`);
        }
        
        function showTyping(userName) {
            typingUsers.add(userName);
            updateTypingIndicatorDisplay();
        }
        
        function showMultipleTyping() {
            typingUsers.add('Alice');
            typingUsers.add('Bob');
            typingUsers.add('Charlie');
            updateTypingIndicatorDisplay();
        }
        
        function hideTyping() {
            typingUsers.clear();
            updateTypingIndicatorDisplay();
        }
        
        function updateStatus(message) {
            document.getElementById('status').textContent = `Status: ${message}`;
        }
        
        // Initialize
        updateStatus('Ready for testing');
    </script>
</body>
</html>
