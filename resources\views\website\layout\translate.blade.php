<style>
    /* Hide Google Translate banner and elements */
    #google-te-banner-frame,
    body>.skiptranslate,
    .goog-te-banner-frame.skiptranslate,
    #goog-gt-tt,
    .goog-te-balloon-frame {
        display: none !important;
    }

    body {
        top: 0px !important;
    }

    /* Loader styles */
    #loader {
        position: fixed;
        width: 100vw;
        height: 100%;
        z-index: 9999999;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 0;
        left: 0;
    }

    /* Remove Google Translate highlighting */
    .goog-text-highlight,
    .VIpgJd-ZVi9od-ORHb {
        background: none !important;
        box-shadow: none !important;
    }
</style>

{{-- Hidden Google Translate Element --}}
<div id="google_translate_element" style="display: none;"></div>

{{-- <script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit" type="text/javascript">
</script> --}}

<script type="text/javascript">
    let selectedLang = '/en/en';

    // Initialize Google Translate with better error handling

    // function googleTranslateElementInit() {
    //     console.log('Initializing Google Translate...');

    //     try {
    //         new google.translate.TranslateElement({
    //             pageLanguage: 'en',
    //             autoDisplay: false,
    //             layout: google.translate.TranslateElement.InlineLayout.SIMPLE
    //         }, 'google_translate_element');

    //         console.log('Google Translate initialized, waiting to apply language...');

    //         // Apply saved language after initialization with longer timeout
    //         setTimeout(function() {
    //             console.log('Attempting to apply saved language after timeout');
    //             applySavedLanguage();
    //         }, 3000); // Increased timeout for better reliability
    //     } catch (error) {
    //         console.error('Error initializing Google Translate:', error);
    //     }
    // }

    // Trigger HTML events for cross-browser compatibility
    function triggerHtmlEvent(element, eventName) {
        let event;
        if (document.createEvent) {
            event = document.createEvent('HTMLEvents');
            event.initEvent(eventName, true, true);
            element.dispatchEvent(event);
        } else {
            event = document.createEventObject();
            event.eventType = eventName;
            element.fireEvent('on' + event.eventType, event);
        }
    }

    // Apply saved language to Google Translate with better error handling
    function applySavedLanguage(retryCount = 0) {
        console.log('Trying to apply saved language...');
        const container = document.getElementById('google_translate_element');
        if (container) {
            const select = container.getElementsByTagName('select')[0];
            if (select) {
                const langCode = selectedLang.split('/')[2]; // '/en/es' -> 'es'
                if (langCode && langCode !== 'en') {
                    //console.log(`Applying language: ${langCode}`);

                    // Check if the option exists before setting
                    const option = select.querySelector(`option[value="${langCode}"]`);
                    if (option) {
                        select.value = langCode;
                        triggerHtmlEvent(select, 'change');
                        console.log('Language applied successfully');
                    } else {
                        //console.log(`Language option ${langCode} not found in select`);
                    }
                } else {
                    //console.log('Language is English, no action needed');
                }
            } else if (retryCount < 15) { // Increased retry count
                //console.log(`Select not ready, retrying applySavedLanguage (${retryCount + 1})`);
                setTimeout(() => applySavedLanguage(retryCount + 1), 300); // Reduced retry interval
            } else {
                //console.log('Failed to apply language: select not found after retries');
            }
        } else {
            //console.log('Google Translate container not found');
        }
    }


    // Website header language switching with URL prefixes (en/es)
    function changeLanguageByButtonClick(element) {
        // Prevent default link behavior
        if (event) {
            event.preventDefault();
        }

        // Prevent multiple clicks during processing
        if ($(element).hasClass('processing')) {
            return false;
        }
        $(element).addClass('processing');

        if ($('.preloader').length) {
            $('.preloader').show();
        }

        const lang = $(element).data("lang");
        //console.log('Website language button clicked:', lang);

        const targetLocale = (lang === 'Spanish' || lang === 'es') ? 'es' : 'en';
        const targetLang = targetLocale === 'es' ? '/en/es' : '/en/en';

        // Clear any existing Google Translate cookies first
        document.cookie = 'googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=.luxustars.com; path=/;';
        document.cookie = 'googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=luxustars.com; path=/;';
        document.cookie = 'googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

        // Set locale session first
        $.ajax({
            url: '/set-locale',
            method: 'POST',
            data: {
                locale: targetLocale,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function() {
                setCookie('googtrans', targetLang, 30);
                selectedLang = targetLang;

                // console.log('Selected language set to:', selectedLang);
                // console.log('Target locale:', targetLocale);
                // Build the new URL with the correct locale
                const currentPath = window.location.pathname;
                const currentSearch = window.location.search;
                const segments = currentPath.split('/').filter(segment => segment.length > 0);

                // Remove existing locale if present
                if (segments.length > 0 && (segments[0] === 'en' || segments[0] === 'es')) {
                    segments.shift();
                }

                // Add new locale
                const newPath = '/' + targetLocale + (segments.length > 0 ? '/' + segments.join('/') : '');
                const newUrl = newPath + currentSearch;

                //console.log('Website redirecting to:', newUrl);
                if (window.location.pathname.match(/^\/message\/me\/.*/) || window.location.pathname ==
                    '/message/me') {
                    location.reload(); // Reload the page if the path matches
                } else {
                    window.location.href = newUrl; // Redirect to the new URL
                }
                // Navigate to the new URL
            },
            error: function(xhr, status, error) {
                //console.error('Error setting locale:', error);
                $(element).removeClass('processing');
                if ($('.preloader').length) {
                    $('.preloader').hide();
                }
            }
        });

        return false;
    }

    // Make function globally available
    window.changeLanguageByButtonClick = changeLanguageByButtonClick;

    // Simple dashboard language switching - no URL prefixes
    function ChangeDashboardLanguage(element) {
        // Prevent default link behavior
        event.preventDefault();

        // Prevent multiple clicks during processing
        if ($(element).hasClass('processing')) {
            return false;
        }
        $(element).addClass('processing');

        if ($('.preloader').length) {
            $('.preloader').show();
        }
        const lang = $(element).data("lang");
       // console.log('Dashboard language button clicked:', lang);
        const targetLocale = (lang === 'Spanish' || lang === 'es') ? 'es' : 'en';
        const targetLang = targetLocale === 'es' ? '/en/es' : '/en/en';

        // Clear existing Google Translate cookies first
        document.cookie = 'googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=.luxustars.com; path=/;';
        document.cookie = 'googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=luxustars.com; path=/;';
        document.cookie = 'googtrans=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

        // Set the new cookie with a small delay
        setTimeout(() => {
            setCookie('googtrans', targetLang, 30);
            selectedLang = targetLang;

            //console.log('Selected language set to:', selectedLang);
            //console.log('Target locale:', targetLocale);

            // Hit the locale URL to set Laravel session
            window.location.href = '/locale/' + targetLocale;
        }, 100);

        return false;
    }

    // Set cookie with proper formatting for luxustars.com
    function setCookie(name, value, days) {
        const date = new Date();
        date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
        const expires = "expires=" + date.toUTCString();

        // Set cookie for luxustars.com domain
        document.cookie = `${name}=${value};${expires};domain=luxustars.com;path=/`;
        document.cookie = `${name}=${value};${expires};domain=.luxustars.com;path=/`;

        // For local development
        document.cookie = `${name}=${value};${expires};path=/`;
    }

    // Check URL locale and set language accordingly
    function getLocaleFromUrl() {
        const path = window.location.pathname;
        const segments = path.split('/').filter(segment => segment.length > 0);

        // Check if first segment is a locale (en or es)
        if (segments.length > 0 && (segments[0] === 'en' || segments[0] === 'es')) {
            return segments[0];
        }

        // Fallback to Laravel's locale
        return '{{ app()->getLocale() }}';
    }

    // Check if cookie exists and set language based on URL locale or Laravel locale
    function checkCookieName(name) {
        // Always prioritize URL locale over cookie
        const urlLocale = getLocaleFromUrl();
        const targetLang = urlLocale === 'es' ? '/en/es' : '/en/en';
        //console.log('URL Locale detected:', urlLocale);
        //console.log('Target language:', targetLang);
        // Check if current cookie matches URL locale
        const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
        if (match) {
            const cookieLang = match[2];
            //console.log('Existing cookie language:', cookieLang);

            // If cookie doesn't match URL locale, update it
            if (cookieLang !== targetLang) {
                //console.log('Cookie language mismatch, updating to match URL locale');
                setCookie('googtrans', targetLang, 30);
                selectedLang = targetLang;

                // Force page reload to ensure translation applies
                setTimeout(() => {
                    //console.log('Forcing page reload to apply translation');
                    window.location.reload();
                }, 500);

                return targetLang;
            } else {
                selectedLang = cookieLang;
                return cookieLang;
            }
        } else {
            // No cookie exists, set based on URL locale
            //console.log('No existing cookie, setting based on URL locale');
            setCookie('googtrans', targetLang, 30);
            selectedLang = targetLang;
            return targetLang;
        }
    }

    // Initialize on page load
    //console.log('Checking cookie on page load...');
    checkCookieName('googtrans');
    //console.log('Initial selectedLang:', selectedLang);

    // Additional event handlers for debugging
    $(document).ready(function() {
        //console.log('Document ready, Google Translate should initialize soon...');

        // Debug: Check if Google Translate element exists after page load
        setTimeout(function() {
            const container = document.getElementById('google_translate_element');
            if (container) {
                //console.log('Google Translate container found');
                const select = container.getElementsByTagName('select')[0];
                if (select) {
                    //console.log('Google Translate select found, options:', select.options.length);
                } else {
                    //console.log('Google Translate select not found yet');
                }
            } else {
                //console.log('Google Translate container not found');
            }
        }, 3000);
    });
</script>
