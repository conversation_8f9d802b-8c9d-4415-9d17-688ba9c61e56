<?php

namespace App\Http\Controllers;

use App\Events\MessageSent;
use App\Models\Conversation;
use App\Models\Message;
use App\Models\User;
use App\Services\PusherService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MessagesController extends Controller
{
    protected $pusherService;
    function __construct(PusherService $pusherService)
    {
        $this->pusherService = $pusherService;
    }
    public function index($conversation_ids = null)
    {
        return view('messages.index', compact('conversation_ids'));
    }

    public function dashboardMessenger()
    {
        return view('messages.dashboard_messenger');
    }


    public function get_conversations(Request $request)
    {
        // Define pagination limit
        $perPage = 5;

        // Build query for private conversations where the user is sender or receiver
        $conversations = Conversation::query()
            ->where('type', 'private')
            ->where(function ($query) {
                $query->where('sender_id', auth()->id())
                    ->orWhere('receiver_id', auth()->id());
            });

        // Apply search filter by name if provided
        if ($request->filled('name')) {
            $conversations->where(function ($query) use ($request) {
                $query->whereHas('receiver', function ($query) use ($request) {
                    $query->where('name', 'like', "%{$request->name}%")
                        ->where('id', '!=', auth()->id());
                })
                ->orWhereHas('sender', function ($query) use ($request) {
                    $query->where('name', 'like', "%{$request->name}%")
                        ->where('id', '!=', auth()->id());
                });
            });
        }

        // Apply role-based filtering
        if ($request->filled('filter') && $request->filter !== 'All') {
            $filter = $request->filter;
            Log::info('Applying conversation filter: ' . $filter);

            if ($filter === 'Provider') {
                // Filter conversations with users who have 'service' role
                $conversations->where(function ($query) {
                    $query->whereHas('sender', function ($q) {
                        $q->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'service');
                        })->where('id', '!=', auth()->id());
                    })
                    ->orWhereHas('receiver', function ($q) {
                        $q->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'service');
                        })->where('id', '!=', auth()->id());
                    });
                });
            } elseif ($filter === 'Traveller') {
                // Filter conversations with users who have 'customer' role
                $conversations->where(function ($query) {
                    $query->whereHas('sender', function ($q) {
                        $q->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'customer');
                        })->where('id', '!=', auth()->id());
                    })
                    ->orWhereHas('receiver', function ($q) {
                        $q->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'customer');
                        })->where('id', '!=', auth()->id());
                    });
                });
            } elseif ($filter === 'Unread') {
                // Filter conversations that have unread messages
                $conversations->whereHas('messages', function ($query) {
                    $query->where('sender_id', '!=', auth()->id())
                          ->whereNull('read_at');
                });
            }
        }

        // Eager-load relationships and paginate
        $conversations = $conversations->with(['sender', 'receiver', 'lastMessage.sender'])
                                    ->orderByRaw('last_message_at IS NULL, last_message_at DESC')
                                    ->orderBy('created_at', 'desc')
                                    ->paginate($perPage);

        // Add unread status and count to each conversation
        foreach ($conversations as $conversation) {
            // Count unread messages for this conversation
            $unreadCount = Message::where('conversation_id', $conversation->id)
                ->where('sender_id', '!=', auth()->id()) // Only messages from others
                ->whereNull('read_at') // Only unread messages
                ->count();

            $conversation->unread_count = $unreadCount;
            $conversation->has_unread = $unreadCount > 0;
        }

        // Return JSON response with rendered view
        return response()->json([
            'status' => true,
            'message' => 'Conversations retrieved successfully',
            'data' => view('messages.include.user_list', compact('conversations'))->render()
        ]);
    }

    /**
     * Search messages within a specific conversation
     */
    public function search_messages(Request $request, $conversation_ids)
    {
        $request->validate([
            'query' => 'required|string|min:1',
            'page' => 'integer|min:1'
        ]);

        try {
            // Find the conversation
            $conversation = Conversation::where('ids', $conversation_ids)
                ->where(function ($query) {
                    $query->where('sender_id', auth()->id())
                        ->orWhere('receiver_id', auth()->id());
                })
                ->firstOrFail();

            $query = $request->input('query');
            $page = $request->input('page', 1);
            $perPage = 20;

            // Search messages in this conversation
            $messages = Message::where('conversation_id', $conversation->id)
                ->where('content', 'like', "%{$query}%")
                ->with(['sender:id,name,avatar'])
                ->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            // Format messages for response
            $formattedMessages = $messages->map(function ($message) {
                return [
                    'id' => $message->id,
                    'content' => $message->content,
                    'sender_name' => $message->sender->name,
                    'sender_avatar' => $message->sender->avatar,
                    'is_own_message' => $message->sender_id == auth()->id(),
                    'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                    'created_at_human' => $message->created_at->diffForHumans(),
                    'time_string' => $message->created_at->format('g:i A')
                ];
            });

            return response()->json([
                'status' => true,
                'message' => 'Messages found',
                'data' => [
                    'messages' => $formattedMessages,
                    'pagination' => [
                        'current_page' => $messages->currentPage(),
                        'total' => $messages->total(),
                        'per_page' => $messages->perPage(),
                        'last_page' => $messages->lastPage(),
                        'has_more_pages' => $messages->hasMorePages()
                    ],
                    'query' => $query
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error searching messages: ' . $e->getMessage()
            ], 500);
        }
    }


    function fetch_conversation(Request $request, $type = "private")
    {
        try{
            $conversation = Conversation::with("sender:id,name,avatar", "receiver:id,name,avatar", "messages")->where("type", $type)->where("ids", $request->conversation_ids)->first();
            if(!$conversation){
                $receiver = User::where("ids", $request->user_ids)->first();
                // $conversation = Conversation::create([
                //     "type" => $type,
                //     "sender_id" => auth()->id(),
                //     "receiver_id" => $receiver->id,
                // ]);
            }
            $receiver = $conversation->sender_id == auth()->id() ? $conversation->receiver : $conversation->sender;
            $conversation->receiver = $receiver;
            return response()->json([
                "status" => true,
                "message" => "Conversation found",
                "data" => view("messages.include.user-info", compact("conversation"))->render()
            ]);
        }catch(\Exception $e){
            return response()->json([
                "status" => false,
                "message" => "Something went wrong"
            ]);
        }
    }

    function parse_message($conversation_ids){
        $conversation = Conversation::where("ids", $conversation_ids)->firstOrFail();

        // Mark messages as read for the current user when opening conversation
        if (request()->get('page', 1) == 1) {
            Message::where('conversation_id', $conversation->id)
                ->where('sender_id', '!=', auth()->id()) // Only mark messages from others as read
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
        }

        // Get pagination parameters
        $page = request()->get('page', 1);
        $perPage = request()->get('per_page', 20); // Consistent with load more messages

        // Get total count
        $totalMessages = Message::where("conversation_id", $conversation->id)->count();
        $totalPages = ceil($totalMessages / $perPage);

        if ($page == 1) {
            // For initial load, get the latest messages (most recent)
            $messages = Message::where("conversation_id", $conversation->id)
                ->with("sender", "conversation")
                ->latest('created_at') // Get latest messages first
                ->take($perPage)
                ->get()
                ->sortBy('created_at'); // Sort by created_at ASC for display

            $hasMorePages = $totalMessages > $perPage;
        } else {
            // For loading older messages (scroll up pagination)
            // Skip the messages we've already loaded
            $skip = ($page - 1) * $perPage;

            $messages = Message::where("conversation_id", $conversation->id)
                ->with("sender", "conversation")
                ->latest('created_at') // Order by latest first
                ->skip($skip)
                ->take($perPage)
                ->get()
                ->sortBy('created_at'); // Sort by created_at ASC for display

            $hasMorePages = ($skip + $perPage) < $totalMessages;
        }

        $message_container = view("messages.include.message-container", [
            'messages' => $messages,
            'hasMorePages' => $hasMorePages,
            'currentPage' => $page
        ])->render();

        return response()->json([
            "status" => true,
            "message" => "Conversation found",
            "data" => $message_container,
            "pagination" => [
                "current_page" => (int) $page,
                "last_page" => (int) $totalPages,
                "per_page" => (int) $perPage,
                "total" => (int) $totalMessages,
                "has_more_pages" => $hasMorePages,
            ]
        ]);
    }

    function load_more_messages($conversation_ids){
        $conversation = Conversation::where("ids", $conversation_ids)->firstOrFail();

        // Get pagination parameters
        $page = request()->get('page', 2);
        $perPage = request()->get('per_page', 20); // Consistent with parse_message

        // Get total count
        $totalMessages = Message::where("conversation_id", $conversation->id)->count();
        $totalPages = ceil($totalMessages / $perPage);

        // Calculate how many messages to skip (for loading older messages)
        $skip = ($page - 1) * $perPage;

        if ($skip < $totalMessages) {
            // Get older messages
            $messages = Message::where("conversation_id", $conversation->id)
                ->with("sender", "conversation")
                ->latest('created_at') // Order by latest first
                ->skip($skip)
                ->take($perPage)
                ->get()
                ->sortBy('created_at'); // Sort by created_at ASC for display

            $hasMorePages = ($skip + $perPage) < $totalMessages;
        } else {
            // No more messages to load
            $messages = collect([]);
            $hasMorePages = false;
        }

        // Use message-chunk view for loading more messages (without loading indicator)
        $message_container = view("messages.include.message-chunk", compact("messages"))->render();

        return response()->json([
            "status" => true,
            "message" => "More messages loaded",
            "data" => $message_container,
            "pagination" => [
                "current_page" => (int) $page,
                "last_page" => (int) $totalPages,
                "per_page" => (int) $perPage,
                "total" => (int) $totalMessages,
                "has_more_pages" => $hasMorePages,
            ]
        ]);
    }
    /**
     * Handle typing indicator start/stop events
     */
    public function typing(Request $request, $conversation_id)
    {
        $request->validate([
            'typing' => 'required'
        ]);

        $sender_user = auth()->user();
        $conversation = Conversation::where("ids", $conversation_id)->firstOrFail();

        // Verify user is part of this conversation
        if ($conversation->sender_id !== $sender_user->id && $conversation->receiver_id !== $sender_user->id) {
            return response()->json([
                "status" => false,
                "message" => "Unauthorized"
            ], 403);
        }

        // Broadcast typing event to the conversation channel
        $this->pusherService->trigger('luxustars-chat-'. $conversation->ids, 'user.typing', [
            'conversation_id' => $conversation->ids,
            'user_id' => $sender_user->ids,
            'user_name' => $sender_user->name,
            'typing' => $request->typing,
            'timestamp' => now()->toISOString()
        ]);

        return response()->json([
            "status" => true,
            "message" => "Typing status updated"
        ]);
    }

    function send_message(Request $request, $conversation_id)
    {
        // Validate input
        $request->validate([
            'message' => 'nullable|string|max:1000',
            'attachment' => 'nullable|file|mimes:jpeg,png,jpg,gif|max:5120' // 5MB max
        ]);

        $sender_user = auth()->user();
        $conversation = Conversation::where("ids", $conversation_id)->firstOrFail();
        $request_message = trim(replacePhoneNumbers($request->message));
        // Validate that either message or attachment is provided
        if (empty($request_message) && !$request->hasFile('attachment')) {
            return response()->json([
                "status" => false,
                "message" => "Message cannot be empty"
            ], 400);
        }

        $message_type = 'text';
        $attachment_data = null;

        // Handle image upload only
        if ($request->hasFile('attachment')) {
            $file = $request->file('attachment');
            $originalName = $file->getClientOriginalName();

            // Store image using storeImage method in public/website/messages directory
            $filePath = $this->storeImage('messages', $file);
            $attachment_url = asset('website/' . $filePath);

            $message_type = 'image';

            $attachment_data = json_encode([
                'original_name' => $originalName,
                'file_path' => $filePath,
                'file_url' => $attachment_url,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ]);
        }

        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $sender_user->id,
            'content' => $request_message ?: ($attachment_data ? 'Sent an image' : ''),
            'message_type' => $message_type,
            'attachment' => $attachment_data,
        ]);

        // Update conversation with latest message info
        $conversation->update([
            'last_message_at' => $message->created_at,
            'last_message_id' => $message->id,
        ]);

        // Prepare message content for pusher (decode for display)
        // $pusher_message = $request_message ? htmlspecialchars_decode($request_message, ENT_QUOTES) : ($message_type === 'image' ? 'Sent a photo' : '');
        $pusher_message = $request_message ? $request_message : ($message_type === 'image' ? 'Sent a photo' : '');

        // event(new MessageSent($conversation));
        $this->pusherService->trigger('luxustars-chat-'. $conversation->ids, 'message.sent', [
            'conversation_id' => $conversation->ids,
            "receiver_id" => $conversation->receiver_user->ids,
            'message' => $pusher_message,
            'timestamp' => now()->toISOString()
        ]);

        // Get receiver's updated unread count
        $receiver_unread_count = $conversation->receiver_user->unread_messages_count();

        $this->pusherService->trigger('luxustars-chat-notification-'. $conversation->receiver_user->ids, 'message.notification', [
            "conversation_ids" => $conversation->ids,
            'sender_name' => $sender_user->name,
            'sender_image' => asset('website/' . $sender_user->avatar ),
            'message' => $pusher_message,
            'unread_count' => $receiver_unread_count,
            'timestamp' => now()->toISOString()
        ]);

        // broadcast(new MessageSent($conversation))->toOthers();
        return response()->json([
            "status" => true,
            "message" => "Message sent",
            "data" => [
                "message_type" => $message_type,
                "message" => $request_message ? htmlspecialchars_decode($request_message, ENT_QUOTES) : ($attachment_data ? 'Sent an image' : ''),
                "attachment" => $attachment_data ? json_decode($attachment_data, true) : null,
                "created_at" => $message->created_at->toISOString(),
                "timestamp" => $message->created_at->timestamp
            ]
        ]);
    }
}
