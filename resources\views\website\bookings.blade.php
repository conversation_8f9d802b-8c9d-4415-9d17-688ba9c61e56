@extends('website.layout.master')
@push('css')
    <style>
        .empty_listing {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 30vh;
            font-size: 30px;
            font-weight: bold;
        }

        .loader {
            border: 16px solid #f3f3f3;
            border-radius: 50%;
            border-top: 16px solid #171717;
            ;
            width: 120px;
            height: 120px;
            -webkit-animation: spin 1s linear infinite;
            /* Safari */
            animation: spin 1s linear infinite;
            display: block;
            margin: 150px auto;
        }


        [tooltip] {
            position: relative;
        }

        [tooltip]::before,
        [tooltip]::after {
            text-transform: none;
            font-size: .9em;
            line-height: 1;
            user-select: none;
            pointer-events: none;
            position: absolute;
            display: none;
            opacity: 0;
        }

        [tooltip]::before {
            content: '';
            border: 5px solid transparent;
            z-index: 1001;
        }

        [tooltip]::after {
            content: attr(tooltip);
            font-family: Helvetica, sans-serif;
            text-align: center;
            min-width: 3em;
            max-width: 21em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 1ch 1.5ch;
            border-radius: .3ch;
            box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35);
            background: #333;
            color: #fff;
            z-index: 1000;
        }

        [tooltip]:hover::before,
        [tooltip]:hover::after {
            display: block;
        }

        [tooltip='']::before,
        [tooltip='']::after {
            display: none !important;
        }

        [tooltip]:not([flow])::before,
        [tooltip][flow^="up"]::before {
            bottom: 100%;
            border-bottom-width: 0;
            border-top-color: #333;
        }

        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::after {
            bottom: calc(100% + 5px);
        }

        [tooltip]:not([flow])::before,
        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::before,
        [tooltip][flow^="up"]::after {
            left: 50%;
            transform: translate(-50%, -.5em);
        }

        [tooltip][flow^="down"]::before {
            top: 100%;
            border-top-width: 0;
            border-bottom-color: #333;
        }

        [tooltip][flow^="down"]::after {
            top: calc(100% + 5px);
        }

        [tooltip][flow^="down"]::before,
        [tooltip][flow^="down"]::after {
            left: 50%;
            transform: translate(-50%, .5em);
        }

        [tooltip][flow^="left"]::before {
            top: 50%;
            border-right-width: 0;
            border-left-color: #333;
            left: calc(0em - 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="left"]::after {
            top: 50%;
            right: calc(100% + 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="right"]::before {
            top: 50%;
            border-left-width: 0;
            border-right-color: #333;
            right: calc(0em - 5px);
            transform: translate(.5em, -50%);
        }

        [tooltip][flow^="right"]::after {
            top: 50%;
            left: calc(100% + 5px);
            transform: translate(.5em, -50%);
        }

        @keyframes tooltips-vert {
            to {
                opacity: .9;
                transform: translate(-50%, 0);
            }
        }

        @keyframes tooltips-horz {
            to {
                opacity: .9;
                transform: translate(0, -50%);
            }
        }

        [tooltip]:not([flow]):hover::before,
        [tooltip]:not([flow]):hover::after,
        [tooltip][flow^="up"]:hover::before,
        [tooltip][flow^="up"]:hover::after,
        [tooltip][flow^="down"]:hover::before,
        [tooltip][flow^="down"]:hover::after {
            animation: tooltips-vert 300ms ease-out forwards;
        }

        [tooltip][flow^="left"]:hover::before,
        [tooltip][flow^="left"]:hover::after,
        [tooltip][flow^="right"]:hover::before,
        [tooltip][flow^="right"]:hover::after {
            animation: tooltips-horz 300ms ease-out forwards;
        }



        /* Safari */
        @-webkit-keyframes spin {
            0% {
                -webkit-transform: rotate(0deg);
            }

            100% {
                -webkit-transform: rotate(360deg);
            }
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Scroll loading styles */
        #scroll-loading {
            position: sticky;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            border-top: 1px solid #e9ecef;
            z-index: 10;
        }

        #scroll-loading .spinner-border-sm {
            width: 1.5rem;
            height: 1.5rem;
            border-width: 0.15em;
        }

        #scroll-loading p {
            font-size: 0.875rem;
            color: #6c757d;
            margin: 0;
        }

        /* Smooth fade in animation for scroll loader */
        #scroll-loading {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Pulse animation for the spinner */
        .spinner-border {
            animation: spinner-border 0.75s linear infinite, pulse 2s ease-in-out infinite alternate;
        }

        @keyframes pulse {
            0% {
                opacity: 0.6;
            }
            100% {
                opacity: 1;
            }
        }
    </style>
@endpush
@section('content')
    <section class="bookings">
        <div class="container">
            <div class="row">
                <div class="col-md-12 pt-5 pb-3 booking_btns">
                    <h2>{{ translate('user_bookings.my_bookings') }}</h2>
                    <div class="past-current">
                        <a class="button " id="past-booking-btn" href="#">{{ translate('user_bookings.past_booking') }}</a>
                        <a class="button style-past-booking" id="current-booking-btn"
                            href="#">{{ translate('user_bookings.on_going_booking') }}</a>
                    </div>
                </div>
                {{-- <div class="col-md-3"> --}}
                {{-- <div class="filter_box">
                        <div class="dropdown">
                            <button class="btn button" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                Filter <i class="bi bi-funnel"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another</a></li>
                                <li><a class="dropdown-item" href="#">Something</a></li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn button" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                Sort <i class="bi bi-chevron-down"></i>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                                <li><a class="dropdown-item" href="#">Action</a></li>
                                <li><a class="dropdown-item" href="#">Another</a></li>
                                <li><a class="dropdown-item" href="#">Something</a></li>
                            </ul>
                        </div>
                    </div> --}}
                {{-- </div> --}}
                <div class="col-md-5 offset-md-7">
                    <div class="search d-flex px-3 py-2 justify-content-between">
                        <input type="text" class="form-control" placeholder="{{ translate('user_bookings.search_here') }}" id="searchInput">
                        <button class="btn search_reset"><i class="fa fa-times" style="color: #4A4A4A;"></i></button>
                        <button class="btn search_booking"><i class="fa fa-search" style="color: #4A4A4A;"></i></button>
                    </div>
                </div>
            </div>
            {{-- loader --}}
            <div id="listing-loading">
                <div class="loader"></div>
            </div>
            {{-- loader end --}}
            <div class="row booking_card_wrapper" id="booking-card-container">

            </div>

            {{-- Small loader for scroll pagination --}}
            <div id="scroll-loading" style="display: none;">
                <div class="text-center py-3">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="sr-only">Loading more...</span>
                    </div>
                    <p class="mt-2 mb-0 text-muted small">{{ translate('common.loading_more') ?? 'Loading more...' }}</p>
                </div>
            </div>
        </div>
    </section>
    @include('website.layout.review')

    {{-- edit modal --}}
    <div class="modal fade review" id="review_edit_modal" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-all ">
            <form action="{{ route('review_update') }}" enctype="multipart/form-data" method="POST">
                <div class="modal-dialog ">
                    <div class="modal-content">
                        @csrf
                        <div class="modal-header justify-content-center">
                            <h3 class="modal-title text-center" id="exampleModalLabel">
                                {{ translate('user_bookings.edit_review') }}
                            </h3>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body mb-3">

                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    {{-- edit modal end --}}



    <!-- Include Report Listing Modal -->
    @include('website.template.modals.report-listing-modal')
@endsection


@push('js')
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

    <script>
        $(document).ready(function() {
            // get past booking with pagination
            let isPastBookingPregress = false;
            let pastBookingCurrentPage = 1;
            let pastBookingHasMorePages = true;

            const get_past_booking = (loadMore = false) => {
                if(isPastBookingPregress || (!loadMore && !pastBookingHasMorePages && pastBookingCurrentPage > 1)) return;
                isPastBookingPregress = true;

                // Show appropriate loader
                if (loadMore) {
                    $('#scroll-loading').show();
                } else {
                    $('#listing-loading').show();
                }

                if (!loadMore) {
                    $("#booking-card-container").empty();
                    pastBookingCurrentPage = 1;
                    pastBookingHasMorePages = true;
                    $(".bookings .search input").val('');
                    $(".bookings .search .search_booking").show();
                    $(".bookings .search .search_reset").hide();
                }

                $.ajax({
                    url: "{{ route('get_past_booking') }}",
                    type: "GET",
                    data: {
                        page: pastBookingCurrentPage,
                        per_page: 10
                    },
                    success: function(response) {
                        if (response.status == true && response.data) {
                            if (loadMore) {
                                $("#booking-card-container").append(response.data.html);
                            } else {
                                $("#booking-card-container").html(response.data.html);
                            }

                            // Update pagination state
                            pastBookingCurrentPage = response.data.current_page + 1;
                            pastBookingHasMorePages = response.data.has_more_pages;

                        } else {
                            if (!loadMore) {
                                $("#booking-card-container").html(
                                    `<div class="empty_listing">
                                        ${response.message}
                                    </div>`
                                );
                            }
                            pastBookingHasMorePages = false;
                        }
                    },
                    complete: function() {
                        isPastBookingPregress = false;
                        $('#listing-loading').hide();
                        $('#scroll-loading').hide();
                    }
                });
            };

            // get current booking with pagination
            let isCurrentBookingPregress = false;
            let currentBookingCurrentPage = 1;
            let currentBookingHasMorePages = true;

            const get_current_booking = (loadMore = false) => {
                if(isCurrentBookingPregress || (!loadMore && !currentBookingHasMorePages && currentBookingCurrentPage > 1)) return;
                isCurrentBookingPregress = true;

                // Show appropriate loader
                if (loadMore) {
                    $('#scroll-loading').show();
                } else {
                    $('#listing-loading').show();
                }

                if (!loadMore) {
                    $("#booking-card-container").empty();
                    currentBookingCurrentPage = 1;
                    currentBookingHasMorePages = true;
                    $(".bookings .search input").val('');
                    $(".bookings .search .search_booking").show();
                    $(".bookings .search .search_reset").hide();
                }

                $.ajax({
                    url: "{{ route('get_current_booking') }}",
                    type: "GET",
                    data: {
                        page: currentBookingCurrentPage,
                        per_page: 10
                    },
                    success: function(response) {
                        if (response.status == true && response.data) {
                            if (loadMore) {
                                $("#booking-card-container").append(response.data.html);
                            } else {
                                $("#booking-card-container").html(response.data.html);
                            }

                            // Update pagination state
                            currentBookingCurrentPage = response.data.current_page + 1;
                            currentBookingHasMorePages = response.data.has_more_pages;

                        } else {
                            if (!loadMore) {
                                $(".Load_more").hide();
                                $("#booking-card-container").html(
                                    `<div class="empty_listing">
                                        ${response.message}
                                    </div>`
                                );
                            }
                            currentBookingHasMorePages = false;
                        }
                    },
                    complete: function() {
                        isCurrentBookingPregress = false;
                        $('#listing-loading').hide();
                        $('#scroll-loading').hide();
                    }
                });
            };

            // if (window.location.hash === '') {
            //     get_current_booking();
            // }

            // Track current active tab
            let activeBookingTab = 'current';

            $("#current-booking-btn").on("click", function() {
                $(this).addClass("style-past-booking");
                $("#past-booking-btn").removeClass("style-past-booking");
                activeBookingTab = 'current';
                resetPaginationState(); // Reset pagination when switching tabs
                get_current_booking();
            });

            $("#past-booking-btn").on("click", function() {
                $(this).addClass("style-past-booking");
                $("#current-booking-btn").removeClass("style-past-booking");
                activeBookingTab = 'past';
                resetPaginationState(); // Reset pagination when switching tabs
                get_past_booking();
            });

            // Scroll event for infinite loading
            let scrollTimeout;
            $(window).on('scroll', function() {
                clearTimeout(scrollTimeout);
                scrollTimeout = setTimeout(function() {
                    const scrollTop = $(window).scrollTop();
                    const windowHeight = $(window).height();
                    const documentHeight = $(document).height();
                    const scrollThreshold = 100; // pixels from bottom

                    if ((documentHeight - scrollTop - windowHeight) <= scrollThreshold) {
                        console.log('Scroll triggered - Active tab:', activeBookingTab);
                        console.log('Current booking - hasMore:', currentBookingHasMorePages, 'inProgress:', isCurrentBookingPregress);
                        console.log('Past booking - hasMore:', pastBookingHasMorePages, 'inProgress:', isPastBookingPregress);

                        if (activeBookingTab === 'current' && currentBookingHasMorePages && !isCurrentBookingPregress) {
                            console.log('Loading more current bookings...');
                            get_current_booking(true); // loadMore = true
                        } else if (activeBookingTab === 'past' && pastBookingHasMorePages && !isPastBookingPregress) {
                            console.log('Loading more past bookings...');
                            get_past_booking(true); // loadMore = true
                        }
                    }
                }, 100); // Throttle scroll events
            });



            const pastBtn = $('#past-booking-btn');
            const currentBtn = $('#current-booking-btn');

            function setActiveTab(activeBtn, inactiveBtn) {
                activeBtn.addClass('style-past-booking');
                inactiveBtn.removeClass('style-past-booking');
            }

            // Function to reset pagination state when switching tabs
            function resetPaginationState() {
                // Reset current booking pagination
                currentBookingCurrentPage = 1;
                currentBookingHasMorePages = true;
                isCurrentBookingPregress = false;

                // Reset past booking pagination
                pastBookingCurrentPage = 1;
                pastBookingHasMorePages = true;
                isPastBookingPregress = false;
            }

            // Click events to update hash in URL
            pastBtn.on('click', function (e) {
                e.preventDefault();
                activeBookingTab = 'past'; // Set the active tab variable
                setActiveTab(pastBtn, currentBtn);
                history.replaceState(null, null, '#past');
            });

            currentBtn.on('click', function (e) {
                e.preventDefault();
                activeBookingTab = 'current'; // Set the active tab variable
                setActiveTab(currentBtn, pastBtn);
                history.replaceState(null, null, '#current');
            });

            // On page load, check hash
            console.log('Page loaded with hash:', window.location.hash);
            if (window.location.hash === '#past') {
                activeBookingTab = 'past'; // Set the active tab variable
                console.log('Initializing past booking tab');
                setActiveTab(pastBtn, currentBtn);
                setTimeout(function () {
                    get_past_booking();
                }, 1000);
            } else if (window.location.hash === '#current') {
                activeBookingTab = 'current'; // Set the active tab variable
                console.log('Initializing current booking tab');
                setActiveTab(currentBtn, pastBtn);
                setTimeout(function () {
                    get_current_booking();
                }, 1000);
            } else {
                // Default tab if no hash
                activeBookingTab = 'current'; // Set the active tab variable
                console.log('Initializing default (current) booking tab');
                setActiveTab(currentBtn, pastBtn);
                get_current_booking();
            }


    

            $(document).on("click", ".report-btn", function() {
                let listing_id = $(this).attr("data-listing-id");
                if (listing_id) {
                    setReportListingId(listing_id);
                }
            })

            $("body").on("click", '.search_booking', function() {
                var value = $(this).closest('.search').find('input').val().toLowerCase();
                searchBooking(value);

            });
            $("body").on("keyup", '.search input', function() {
                var value = $(this).closest('.search').find('input').val().toLowerCase();
                if (event.keyCode === 13) {
                    searchBooking(value);
                }
            });
            $("body").on("input", '#searchInput', function() {
                var value = $(this).val().toLowerCase(); // Get the current value of input field
                searchBooking(value); // Call the search function with the current value
            });


            $("body").delegate('.search_reset', "click", function() {
                var value = $(this).closest('.search').find('input').val('');
                var rowCount = 0;

                $(this).hide();
                $('.search_booking').show();

                $(".booking_card_wrapper .booking_cards_parent").each(function() {
                    $(this).removeAttr('style');
                });

                $('.no-data-found').remove();

                var selectedTab = $('.bookings .past-current .style-past-booking').attr('id');
                if (selectedTab == 'past-booking-btn') {
                    get_past_booking();
                } else {
                    get_current_booking();
                }

            });

            //Updated searchBooking function
            function searchBooking(value) {
                var rowCount = 0;
                $('#listing-loading').show();
                $('.booking_cards_parent').hide();
                $('.no_found_data').remove();
                $('.empty_listing').hide();

                setTimeout(function() {
                    $(".booking_card_wrapper .booking_cards_parent").filter(function() {
                        var bookingId = $(this).find('[data-booking-id]').attr('data-booking-id') ||
                            "";
                        var hostName = $(this).find('.host_name').text().toLowerCase() || "";
                        var listingName = $(this).find('.listing_name').text().toLowerCase() || "";
                        var match = false;

                        // Search for the numeric part of the booking ID as well as the full booking ID
                        var searchTerms = value.split(
                            ' '); // Split search value into multiple terms
                        searchTerms.forEach(function(term) {
                            // Checking if the search term matches with the booking ID, Host Name, or Listing Name
                            if (bookingId.toLowerCase().indexOf(term) > -1 || hostName
                                .indexOf(term) > -1 || listingName.indexOf(term) > -1) {
                                match = true;
                            }

                            // If only the numeric part of the booking ID is typed, we match against the numeric part
                            if (term.match(/^\d+$/) && bookingId.replace(/\D/g, '').indexOf(
                                    term) > -1) {
                                match = true;
                            }
                        });

                        $(this).toggle(match); // Show/hide element based on match

                        if (match) rowCount++;
                    });

                    console.log("Total matches:", rowCount);

                    $('#listing-loading').hide();

                    if (rowCount === 0) {
                        if ($('.booking_card_wrapper').find('.no_found_data').length == 0) {
                            $(".booking_card_wrapper").append(
                                '<h3 class="no_found_data pt-5 text-center bold"><span class="no-data-found">{{ translate('user_bookings.no_booking_found') }}</span></h3>'
                            );
                            // $('.empty_listing').hide();
                        }
                    } else {
                        $('.no_found_data').remove();
                    }
                    $('.search_reset').show();
                    $('.search_booking').hide();
                    
                }, 300);
            }





            let loader = `<div id="listing-loading"><div class="loader"></div></div>`;
            $(document).on("click", ".review-btn", function() {
                let booking_id = $(this).attr("data-booking-id");
                $("#review .modal-body").html(loader);
                $.ajax({
                    url: "{{ route('get_review_form') }}",
                    type: "GET",
                    data: {
                        booking_id: booking_id
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#review .modal-body").html(response.data);
                        } else {
                            Swal.fire({
                                title: "Error!",
                                text: response.message,
                                icon: "error"
                            });
                        }
                        window.validateRatings();
                        $(document).on("change", ".rating-checkbox", window.validateRatings);

                    }
                });
            })

            // edit review
            $(document).on("click", ".edit-review-btn", function() {
                let review_id = $(this).attr("data-review-id");
                $("#review_edit_modal .modal-body").html(loader);
                if (review_id) {
                    $.ajax({
                        url: `{{ route('edit_review_form', '') }}/${review_id}`,
                        type: "GET",
                        success: function(response) {
                            if (response.status == true) {
                                $("#review_edit_modal .modal-body").html(response.data);
                                $("#review_edit_modal").modal('show');
                            } else {
                                Swal.fire({
                                    title: "Error!",
                                    text: response.message,
                                    icon: "error"
                                });
                            }

                            window.validateRatings();
                            $(document).on("change", ".rating-checkbox", window
                                .validateRatings);
                        }
                    });
                }
            })
            // edit review end
        });
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.6/Sortable.min.js"></script>

    {{-- HEIC To any CDN for converting HEIC images to previewable format --}}
    {{-- <script src="https://cdn.jsdelivr.net/npm/heic2any@0.0.4/dist/heic2any.min.js"></script> --}}
    {{-- <script src="{{asset('website')}}/assets_cdn/js/heic2any.min.js"></script> --}}

    <script>
        // $(document).ready(function() {
        //     const sortableContainer = $('.drag_drop_photos_wrapper');
        //     new Sortable(sortableContainer[0], {
        //         animation: 150,
        //         draggable: '.sortable-element',
        //         onEnd: function() {
        //             const image_ids = sortableContainer
        //                 .children('.sortable-element')
        //                 .map(function() {
        //                     return $(this).data('image-id');
        //                 })
        //                 .get();
        //         },
        //     });
        // });

        // $(document).on('change', '#add_photo_file', function(event) {
        //     const files = event.target.files;
        //     if (files.length === 0) {
        //         alert("No files selected.");
        //         return;
        //     }

        //     const sortableContainer = $('.drag_drop_photos_wrapper');

        //     Swal.fire({
        //         title: "Uploading Images",
        //         html: `Uploading <b>0</b> out of ${files.length}`,
        //         allowOutsideClick: false,
        //         showConfirmButton: false,
        //         didOpen: () => {
        //             Swal.showLoading();
        //         },
        //     });

        //     let uploadedCount = 0;
        //     const totalFiles = files.length;

        //     for (let i = 0; i < files.length; i++) {
        //         let file = files[i];
        //         let reader = new FileReader();

        //         reader.onload = function(e) {
        //             uploadedCount++;
        //             Swal.update({
        //                 html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
        //             });

        //             const newDiv = $(
        //                 `<div class="drag_drop_photo_single sortable-element">
    //                     <img alt="Preview ${i}" loading="lazy" src="${e.target.result}">
    //                     <div class="delete_btn_wrapper">
    //                         <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
    //                     </div>
    //                 </div>`
        //             );
        //             const addPhotoBox = $(
        //                 '.drag_drop_photo_single.add_photo_box'
        //             );
        //             newDiv.insertBefore(addPhotoBox);
        //         };

        //         reader.readAsDataURL(file);
        //     }

        //     setTimeout(() => {
        //         if (sortableContainer.length && typeof Sortable !== "undefined") {
        //             new Sortable(sortableContainer[0], {
        //                 animation: 150,
        //                 draggable: '.sortable-element',
        //                 onEnd: function() {
        //                     const image_ids = sortableContainer
        //                         .children('.sortable-element')
        //                         .map(function() {
        //                             return $(this).data('image-id');
        //                         })
        //                         .get();
        //                 },
        //             });
        //         }
        //     }, 500);

        //     Swal.fire({
        //         title: "Upload Complete",
        //         text: "All images have been uploaded successfully!",
        //         icon: "success",
        //         timer: 1500,
        //         showConfirmButton: false
        //     });
        // });

        // $(document).on('change', '#add_photo_file', function(event) {
        //     const files = event.target.files;

        //     const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        //     const minSize = 300 * 1024;
        //     const maxSize = 10 * 1024 * 1024; // 10MB
        //     const maxFiles = 10; // number of images per upload
        //     let validImages = []; // Store valid images
        //     let validImageCount = 0;

        //     if (files.length === 0) {
        //         alert("No files selected.");
        //         return;
        //     }

        //     const sortableContainer = $('.drag_drop_photos_wrapper');

        //     Swal.fire({
        //         title: "Uploading Images",
        //         html: `Uploading <b>0</b> out of ${files.length}`,
        //         allowOutsideClick: false,
        //         showConfirmButton: false,
        //         didOpen: () => {
        //             Swal.showLoading();
        //         },
        //     });

        //     let uploadedCount = 0;
        //     const totalFiles = files.length;

        //     const resizeImage = (file, maxWidth = 300, maxHeight = 300) => {
        //         return new Promise((resolve) => {
        //             const reader = new FileReader();
        //             reader.onload = function(e) {
        //                 const img = new Image();
        //                 img.onload = function() {
        //                     let canvas = document.createElement("canvas");
        //                     let ctx = canvas.getContext("2d");

        //                     let ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
        //                     canvas.width = img.width * ratio;
        //                     canvas.height = img.height * ratio;

        //                     ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        //                     resolve(canvas.toDataURL("image/jpeg", 0.7));
        //                 };
        //                 img.src = e.target.result;
        //             };
        //             reader.readAsDataURL(file);
        //         });
        //     };

        //     const processFiles = async () => {
        //         for (let i = 0; i < files.length; i++) {
        //             let resizedDataUrl = await resizeImage(files[i]);

        //             uploadedCount++;
        //             Swal.update({
        //                 html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
        //             });

        //             const newDiv = $(`
    //                 <div class="drag_drop_photo_single sortable-element">
    //                     <img alt="Preview ${i}" loading="lazy" src="${resizedDataUrl}">
    //                     <div class="delete_btn_wrapper">
    //                         <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
    //                     </div>
    //                 </div>
    //             `);

        //             const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
        //             newDiv.insertBefore(addPhotoBox);
        //         }

        //         setTimeout(() => {
        //             if (sortableContainer.length && typeof Sortable !== "undefined") {
        //                 new Sortable(sortableContainer[0], {
        //                     animation: 150,
        //                     draggable: '.sortable-element',
        //                     onEnd: function() {
        //                         const image_ids = sortableContainer
        //                             .children('.sortable-element')
        //                             .map(function() {
        //                                 return $(this).data('image-id');
        //                             })
        //                             .get();
        //                     },
        //                 });
        //             }
        //         }, 500);

        //         Swal.fire({
        //             title: "Upload Complete",
        //             text: "All images have been uploaded successfully!",
        //             icon: "success",
        //             timer: 1500,
        //             showConfirmButton: false
        //         });
        //     };

        //     processFiles();
        // });



        // $(document).on('change', '#add_photo_file', function(event) {
        //     const files = event.target.files;
        //     const maxFiles = 5;

        //     // Count existing previewed images (exclude add_photo_box)
        //     const existingImages = $('.drag_drop_photo_single').not('.add_photo_box').length;

        //     // Check total preview limit
        //     if (existingImages + files.length > maxFiles) {
        //         Swal.fire({
        //             icon: 'error',
        //             title: 'Photo Limit Reached',
        //             text: `Maximum ${maxFiles} photos allowed per review.`,
        //         });
        //         return;
        //     }

        //     // Check if any files are selected
        //     if (files.length === 0) {
        //         Swal.fire({
        //             icon: 'warning',
        //             title: 'No Files Selected',
        //             text: 'Please select at least one file to upload.',
        //         });
        //         return;
        //     }

        //     const sortableContainer = $('.drag_drop_photos_wrapper');

        //     Swal.fire({
        //         title: "Uploading Images",
        //         html: `Uploading <b>0</b> out of ${files.length}`,
        //         allowOutsideClick: false,
        //         showConfirmButton: false,
        //         didOpen: () => {
        //             Swal.showLoading();
        //         },
        //     });

        //     let uploadedCount = 0;
        //     const totalFiles = files.length;

        //     const resizeImage = (file, maxWidth = 600, maxHeight = 600) => {
        //         return new Promise((resolve, reject) => {
        //             // Check if file is HEIF/HEIC
        //             const fileName = file.name.toLowerCase();
        //             const isHeif = fileName.endsWith('.heif') || fileName.endsWith('.heic') ||
        //                           file.type === 'image/heif' || file.type === 'image/heic' ||
        //                           file.type === '' && (fileName.endsWith('.heif') || fileName.endsWith('.heic'));

        //             if (isHeif) {
        //                 console.log('Processing HEIF/HEIC file:', file.name);
        //                 // For HEIF/HEIC files, just return the original file as data URL without resizing
        //                 // since browsers don't natively support HEIF in canvas
        //                 const reader = new FileReader();
        //                 reader.onload = function(e) {
        //                     console.log('HEIF file read successfully:', file.name);
        //                     resolve(e.target.result);
        //                 };
        //                 reader.onerror = function(error) {
        //                     console.error('Failed to read HEIF file:', file.name, error);
        //                     reject(new Error('Failed to read HEIF file'));
        //                 };
        //                 reader.readAsDataURL(file);
        //                 return;
        //             }

        //             // For other image formats (JPEG, PNG)
        //             const reader = new FileReader();
        //             reader.onload = function(e) {
        //                 const img = new Image();

        //                 // Add error handler for image loading
        //                 img.onerror = function() {
        //                     console.error('Failed to load image:', file.name);
        //                     // Fallback: return original file as data URL
        //                     resolve(e.target.result);
        //                 };

        //                 img.onload = function() {
        //                     try {
        //                         let canvas = document.createElement("canvas");
        //                         let ctx = canvas.getContext("2d");

        //                         let ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
        //                         canvas.width = img.width * ratio;
        //                         canvas.height = img.height * ratio;

        //                         ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        //                         resolve(canvas.toDataURL("image/jpeg", 1));
        //                     } catch (error) {
        //                         console.error('Canvas processing failed:', error);
        //                         // Fallback: return original file as data URL
        //                         resolve(e.target.result);
        //                     }
        //                 };
        //                 img.src = e.target.result;
        //             };

        //             reader.onerror = function() {
        //                 reject(new Error('Failed to read file'));
        //             };

        //             reader.readAsDataURL(file);
        //         });
        //     };

        //     const processFiles = async () => {
        //         const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heif', 'image/heic'];
        //         const validExtensions = ['.jpg', '.jpeg', '.png', '.heif', '.heic'];
        //         const maxSize = 10 * 1024 * 1024;
        //         let validImages = [];
        //         let validImageCount = 0;

        //         // Helper function to check if file is valid
        //         const isValidImageFile = (file) => {
        //             // First check MIME type
        //             if (file.type && validTypes.includes(file.type)) {
        //                 return true;
        //             }

        //             // Fallback: check file extension for files with empty/unknown MIME type (like HEIC)
        //             const fileName = file.name.toLowerCase();
        //             return validExtensions.some(ext => fileName.endsWith(ext));
        //         };

        //         for (let i = 0; i < files.length; i++) {
        //             let file = files[i];
        //             console.log(`Processing file: ${file.name}, Type: "${file.type}", Size: ${file.size}, Extension: ${file.name.split('.').pop()}`);
        //             const errorMessage = $('<div class="error-message"></div>');
        //             const imageUrl = URL.createObjectURL(file);
        //             if (!isValidImageFile(file)) {
        //                 errorMessage.text(`❌ Invalid file type: "${file.name}". Supported formats: JPG, PNG, HEIC, HEIF`);
        //                 const newDiv = $(`
        //                 <div class="drag_drop_photo_single sortable-element error">
        //                     <img alt="Preview ${i}" loading="lazy" src="${imageUrl}">
        //                     ${errorMessage[0].outerHTML}
        //                     <div class="delete_btn_wrapper">
        //                         <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
        //                     </div>
        //                 </div>
        //             `);
        //                 const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
        //                 newDiv.insertBefore(addPhotoBox);
        //                 continue;
        //             }
        //             if (file.size > maxSize) {
        //                 errorMessage.text(`⚠️ File size not allowed: "${file.name}" (must be less than 10MB)`);
        //                 const newDiv = $(`
        //                 <div class="drag_drop_photo_single sortable-element warning">
        //                     <img alt="Preview ${i}" loading="lazy" src="${imageUrl}">
        //                     ${errorMessage[0].outerHTML}
        //                     <div class="delete_btn_wrapper">
        //                         <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
        //                     </div>
        //                 </div>
        //             `);
        //                 const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
        //                 newDiv.insertBefore(addPhotoBox);
        //                 continue;
        //             }
        //             validImages.push(file);
        //             validImageCount++;

        //             try {
        //                 let resizedDataUrl = await resizeImage(file);
        //                 uploadedCount++;
        //                 Swal.update({
        //                     html: `Uploading <b>${uploadedCount}</b> out of ${totalFiles}...`,
        //                 });

        //                 const newDiv = $(`
        //                     <div class="drag_drop_photo_single sortable-element">
        //                         <img alt="Preview ${i}" loading="lazy" src="${resizedDataUrl}">
        //                         <div class="delete_btn_wrapper">
        //                             <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
        //                         </div>
        //                     </div>
        //                 `);
        //                 const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
        //                 newDiv.insertBefore(addPhotoBox);

        //             } catch (error) {
        //                 console.error('Error processing file:', file.name, error);

        //                 // Show error for this specific file
        //                 const errorMessage = $('<div class="error-message"></div>');
        //                 errorMessage.text(`❌ Failed to process: "${file.name}"`);

        //                 const imageUrl = URL.createObjectURL(file);
        //                 const newDiv = $(`
        //                     <div class="drag_drop_photo_single sortable-element error">
        //                         <img alt="Preview ${i}" loading="lazy" src="${imageUrl}">
        //                         ${errorMessage[0].outerHTML}
        //                         <div class="delete_btn_wrapper">
        //                             <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
        //                         </div>
        //                     </div>
        //                 `);
        //                 const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
        //                 newDiv.insertBefore(addPhotoBox);

        //                 uploadedCount++; // Still count it as processed
        //                 Swal.update({
        //                     html: `Processing <b>${uploadedCount}</b> out of ${totalFiles}...`,
        //                 });
        //             }
        //         }

        //         // Check if any valid images were processed
        //         // if (validImageCount === 0) {
        //         //     Swal.close();
        //         //     Swal.fire({
        //         //         icon: 'error',
        //         //         title: 'No Valid Images',
        //         //         text: 'No valid images were uploaded.',
        //         //     });
        //         //     return;
        //         // }
        //         // Initialize Sortable.js
        //         setTimeout(() => {
        //             if (sortableContainer.length && typeof Sortable !== "undefined") {
        //                 new Sortable(sortableContainer[0], {
        //                     animation: 150,
        //                     draggable: '.sortable-element',
        //                     onEnd: function() {
        //                         const image_ids = sortableContainer
        //                             .children('.sortable-element')
        //                             .map(function() {
        //                                 return $(this).data('image-id');
        //                             })
        //                             .get();
        //                     },
        //                 });
        //             }
        //         }, 500);
        //         // Show success message
        //         Swal.fire({
        //             title: "Upload Complete",
        //             text: `${validImageCount} image(s) uploaded successfully!`,
        //             icon: "success",
        //             timer: 1500,
        //             showConfirmButton: false
        //         });
        //     };
        //     processFiles();
        // });


        // New code for review image upload HEIC and HEIF format


        $(document).on('change', '#add_photo_file', async function(event) {
            const files = event.target.files;
            const maxFiles = 5;
            const isEditModal = $(this).closest('#review_edit_modal').length > 0;

            const existingImages = $(this).closest('.drag_drop_photos_wrapper').find('.drag_drop_photo_single').not('.add_photo_box').length;
            console.log('Existing images:', existingImages);
            
            const maxFilesText = `{{ translate('user_review_popup.max_photos_per_review_text') }}`;
            const translatedText = maxFilesText.replace(':max', maxFiles);

            if (existingImages + files.length > maxFiles) {
                Swal.fire({
                    icon: 'error',
                    title: `{{ translate('user_review_popup.photo_limit_reached') }}`,
                    // text: `Maximum ${maxFiles} photos allowed per review.`,
                    text: translatedText,
                });
                return;
            }

            if (files.length === 0) {
                Swal.fire({
                    icon: 'warning',
                    title: 'No Files Selected',
                    text: 'Please select at least one file to upload.',
                });
                return;
            }

            const sortableContainer = $('.drag_drop_photos_wrapper');
            const processedFiles = [];

            const swalInstance = Swal.fire({
                title: `{{ translate('user_review_popup.processing_images') }}`,
                html: `{{ translate('user_review_popup.processing_files') }} <b>0</b> {{ translate('user_review_popup.out_of') }} ${files.length}`,
                allowOutsideClick: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                },
            });

            try {
                if (!window.heic2any && Array.from(files).some(f => 
                    f.name.toLowerCase().endsWith('.heic') || 
                    f.name.toLowerCase().endsWith('.heif') ||
                    f.type === 'image/heic' || 
                    f.type === 'image/heif'
                )) {
                    await loadHeic2AnyLibrary();
                }

                let processedCount = 0;
                const processPromises = [];

                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    processPromises.push(
                        processSingleFile(file, i)
                            .then(({ imageUrl, processedFile }) => {
                                processedCount++;
                                processedFiles.push(processedFile);
                                addImagePreview(imageUrl, i, processedFile);
                                swalInstance.update({
                                    html: `{{ translate('user_review_popup.processing_files') }} <b>${processedCount}</b> {{ translate('user_review_popup.out_of') }} ${files.length}...`,
                                });
                            })
                            .catch(error => {
                                console.error(`Error processing file ${file.name}:`, error);
                                showErrorPreview(file, i, error.message || 'Failed to process image');
                            })
                    );
                }

                await Promise.all(processPromises);

                const dataTransfer = new DataTransfer();
                processedFiles.forEach(file => {
                    console.log('Adding to DataTransfer:', file.name, file.type, file.size);
                    dataTransfer.items.add(file);
                });
                this.files = dataTransfer.files;
                console.log('Files attached to input:', this.files);

                if (sortableContainer.length && typeof Sortable !== "undefined") {
                    new Sortable(sortableContainer[0], {
                        animation: 150,
                        draggable: '.sortable-element',
                        onEnd: function() {
                            const image_ids = sortableContainer
                                .children('.sortable-element')
                                .map(function() {
                                    return $(this).data('image-id') || null;
                                })
                                .get();
                            console.log('Sorted image IDs:', image_ids);
                        },
                    });
                }

                await Swal.fire({
                    title: `{{ translate('user_review_popup.processing_complete') }}`,
                    text: `${processedCount} {{ translate('user_review_popup.image_processed_successfully') }}`,
                    icon: "success",
                    timer: 1500,
                    showConfirmButton: false
                });

            } catch (error) {
                console.error('Global processing error:', error);
                Swal.fire({
                    title: `{{ translate('user_review_popup.processing_error') }}`,
                    text: `{{ translate('user_review_popup.error_occurred_processing_images') }}`,
                    icon: "error"
                });
            }
        });

        function loadHeic2AnyLibrary() {
            return new Promise((resolve, reject) => {
                if (window.heic2any) return resolve();

                const script = document.createElement('script');
                script.src = "{{asset('website')}}/assets_cdn/js/heic2any.min.js";
                script.onload = resolve;
                script.onerror = () => reject(new Error('Failed to load HEIC conversion library'));
                document.head.appendChild(script);
            });
        }

        async function processSingleFile(file, index) {
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heif', 'image/heic'];
            const validExtensions = ['.jpg', '.jpeg', '.png', '.heif', '.heic'];
            const maxSize = 10 * 1024 * 1024;

            const fileName = file.name.toLowerCase();
            const isHeic = fileName.endsWith('.heic') || fileName.endsWith('.heif') || 
                        file.type === 'image/heic' || file.type === 'image/heif';
            const isValidType = file.type && validTypes.includes(file.type);
            const isValidExtension = validExtensions.some(ext => fileName.endsWith(ext));

            if (!isValidType && !isValidExtension) {
                // throw new Error(`Invalid file type: "${file.name}". Supported formats: JPG, PNG, HEIC, HEIF`);
                throw new Error(@json(translate('user_review_popup.invalid_file_type_error')).replace(':file_format', file.name));
            }

            if (file.size > maxSize) {
                // throw new Error(`File too large: "${file.name}" (must be less than 10MB)`);
                throw new Error(@json(translate('user_review_popup.file_too_large_error')).replace(':file_name', file.name));
            }

            const { imageUrl, processedFile } = await processImageFile(file);
            return { imageUrl, processedFile };
        }

        async function processImageFile(file) {
            const fileName = file.name.toLowerCase();
            const isHeic = fileName.endsWith('.heic') || fileName.endsWith('.heif') || 
                        file.type === 'image/heic' || file.type === 'image/heif';

            let processedFile = file;

            if (isHeic && window.heic2any) {
                try {
                    const convertedBlob = await heic2any({
                        blob: file,
                        toType: 'image/jpeg',
                        quality: 0.8
                    });
                    processedFile = new File([convertedBlob], file.name.replace(/\.[^/.]+$/, '.jpg'), {
                        type: 'image/jpeg',
                        lastModified: Date.now()
                    });
                    console.log('HEIC converted to JPEG:', processedFile.name, processedFile.type, processedFile.size);
                } catch (error) {
                    console.error('HEIC conversion failed:', file.name, error);
                    throw new Error('Failed to convert HEIC image');
                }
            }

            const imageUrl = await resizeImage(processedFile);
            return { imageUrl, processedFile };
        }

        async function resizeImage(file, maxWidth = 600, maxHeight = 600) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        try {
                            let canvas = document.createElement("canvas");
                            let ctx = canvas.getContext("2d");
                            let ratio = Math.min(maxWidth / img.width, maxHeight / img.height);
                            canvas.width = img.width * ratio;
                            canvas.height = img.height * ratio;
                            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                            resolve(canvas.toDataURL("image/jpeg", 0.9));
                        } catch (error) {
                            console.error('Canvas processing failed:', error);
                            resolve(e.target.result);
                        }
                    };
                    img.onerror = function() {
                        console.error('Failed to load image:', file.name);
                        resolve(e.target.result);
                    };
                    img.src = e.target.result;
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        function addImagePreview(imageUrl, index, processedFile) {
            const newDiv = $(`
                <div class="drag_drop_photo_single sortable-element" data-image-id="${index}">
                    <img alt="Preview ${index}" loading="lazy" src="${imageUrl}">
                    <div class="delete_btn_wrapper">
                        <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                    </div>
                </div>
            `);
            newDiv.data('file', processedFile);
            const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
            newDiv.insertBefore(addPhotoBox);
        }

        function showErrorPreview(file, index, errorMessage) {
            const imageUrl = URL.createObjectURL(file);
            const errorDiv = $('<div class="error-message"></div>').text(errorMessage);
            
            const newDiv = $(`
                <div class="drag_drop_photo_single sortable-element error" data-image-id="${index}">
                    <img alt="Preview ${index}" loading="lazy" src="${imageUrl}">
                    ${errorDiv[0].outerHTML}
                    <div class="delete_btn_wrapper">
                        <a class="delete_btn" href="#"><i class="fa fa-trash" aria-hidden="true"></i></a>
                    </div>
                </div>
            `);
            
            const addPhotoBox = $('.drag_drop_photo_single.add_photo_box');
            newDiv.insertBefore(addPhotoBox);
        }

        $(document).on('submit', '.review.modal form, #review_edit_modal form', async function(event) {
            event.preventDefault();
            const form = this;
            const isEditModal = $(form).closest('#review_edit_modal').length > 0;
            const formData = new FormData(form);
            
            // Clear existing photos/images field
            formData.delete(isEditModal ? 'images[]' : 'photos[]');
            
            const processedFiles = $('.drag_drop_photo_single.sortable-element')
                .not('.error')
                .map(function() {
                    return $(this).data('file');
                })
                .get()
                .filter(file => file !== undefined);
            
            processedFiles.forEach((file, index) => {
                console.log('Appending to FormData:', file.name, file.type, file.size);
                formData.append(`${isEditModal ? 'images' : 'photos'}[${index}]`, file);
            });
            
            try {
                const response = await $.ajax({
                    url: $(form).attr('action'),
                    type: $(form).attr('method'),
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
                console.log('Form submission response:', response);
                Swal.fire({
                    title: `{{ translate('user_review_popup.submission_successful') }}`,
                    text: isEditModal ? `{{ translate('user_review_popup.review_updated_successfully') }}` : `{{ translate('user_review_popup.review_submitted_successfully') }}`,
                    icon: "success",
                    timer: 1500,
                    showConfirmButton: false,
                    didClose: () => {
                        window.location.reload();
                    }
                });
            } catch (error) {
                console.error('Form submission failed:', error);
                Swal.fire({
                    title: `{{ translate('user_review_popup.review_submission_error') }}`,
                    text: isEditModal ? `{{ translate('user_review_popup.failed_update_review') }}` : `{{ translate('user_review_popup.failed_submit_review') }}`,
                    icon: "error"
                });
            }
        });

        $(document).on('click', '.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn', function(e) {
            e.preventDefault();
            const listing_image = $(this);
            const closestContainer = listing_image.closest('.drag_drop_photo_single');
            const imageId = closestContainer.data('image-id');
            const isEditModal = listing_image.closest('#review_edit_modal').length > 0;
            console.log('isEditModal:', isEditModal);
            
            const hasFile = closestContainer.data('file');

            console.log('Delete button clicked:', {
                isEditModal: isEditModal,
                imageId: imageId,
                hasFile: hasFile,
                condition: isEditModal && imageId && !hasFile,
                modalId: listing_image.closest('.modal').attr('id'),
                closestContainer: closestContainer[0].outerHTML
            });

            Swal.fire({
                title: `{{ translate('user_review_popup.are_you_sure') }}`,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#ffce32",
                cancelButtonColor: "#d33",
                confirmButtonText: `{{ translate('user_review_popup.yes_delete_it') }}`,
                cancelButtonText: `{{ translate('user_review_popup.cancel') }}`
            }).then((result) => {
                if (result.isConfirmed) {
                    closestContainer.remove();
                    const remainingFiles = $('.drag_drop_photo_single.sortable-element')
                        .not('.error')
                        .map(function() {
                            return $(this).data('file');
                        })
                        .get()
                        .filter(file => file !== undefined);

                    const dataTransfer = new DataTransfer();
                    remainingFiles.forEach(file => dataTransfer.items.add(file));
                    $('#add_photo_file')[0].files = dataTransfer.files;

                    if (isEditModal && imageId && !hasFile) {
                        console.log('Sending AJAX DELETE to:', `/review-image/${imageId}`);
                        $.ajax({
                            url: `/review-image/${imageId}`,
                            method: 'DELETE',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: () => {
                                console.log('Image deleted from backend');
                                Swal.fire({
                                    title: "Deleted!",
                                    text: "Your file has been deleted from the database.",
                                    icon: "success",
                                    timer: 1500,
                                    showConfirmButton: false
                                });
                            },
                            error: (error) => {
                                console.error('Failed to delete image:', error);
                                Swal.fire({
                                    title: "Deletion Error",
                                    text: "Failed to delete the image from the database.",
                                    icon: "error"
                                });
                            }
                        });
                    } else {
                        console.log('AJAX not sent. Condition failed:', { isEditModal, imageId, hasFile });
                        Swal.fire({
                            title: `{{ translate('user_review_popup.deleted') }}`,
                            text: `{{ translate('user_review_popup.your_file_has_been_deleted') }}`,
                            icon: "success",
                            timer: 1500,
                            showConfirmButton: false
                        });
                    }
                }
            });
        });


        // New code ends here


        // $(document).on('click',
        //     '.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn',
        //     function() {
        //         let listing_image = $(this);
        //         let imageId = listing_image.data('image-id');
        //         var listing_id = $("input[name='listing_id']").val();

        //         Swal.fire({
        //             title: "Are you sure?",
        //             // text: "You won't be able to revert this!",
        //             icon: "warning",
        //             showCancelButton: true,
        //             confirmButtonColor: "#ffce32",
        //             cancelButtonColor: "#d33",
        //             confirmButtonText: "Yes, delete it!"
        //         }).then((result) => {
        //             if (result.isConfirmed) {
        //                 listing_image.closest('.drag_drop_photo_single').remove();
        //                 Swal.fire({
        //                     title: "Deleted!",
        //                     text: "Your file has been deleted.",
        //                     icon: "success"
        //                 });
        //             }
        //         });


        //     });


        // $(document).on('click', '.drag_drop_photos_wrapper .drag_drop_photo_single .delete_btn', function(e) {
        //     e.preventDefault();
        //     const listing_image = $(this);
        //     const imageId = listing_image.data('image-id');
        //     const listing_id = $("input[name='listing_id']").val();

        //     Swal.fire({
        //         title: "Are you sure?",
        //         icon: "warning",
        //         showCancelButton: true,
        //         confirmButtonColor: "#ffce32",
        //         cancelButtonColor: "#d33",
        //         confirmButtonText: "Yes, delete it!"
        //     }).then((result) => {
        //         if (result.isConfirmed) {
        //             listing_image.closest('.drag_drop_photo_single').remove();
        //             const remainingFiles = $('.drag_drop_photo_single.sortable-element')
        //                 .not('.error')
        //                 .map(function() {
        //                     return $(this).data('file');
        //                 })
        //                 .get()
        //                 .filter(file => file !== undefined);

        //             const dataTransfer = new DataTransfer();
        //             remainingFiles.forEach(file => dataTransfer.items.add(file));
        //             $('#add_photo_file')[0].files = dataTransfer.files;

        //             Swal.fire({
        //                 title: "Deleted!",
        //                 text: "Your file has been deleted.",
        //                 icon: "success"
        //             });
        //         }
        //     });
        // });

        window.validateRatings = function() {
            let allRated = true;

            $(".rating_star_wrapper").each(function() {
                if (!$(this).find(".rating-checkbox:checked").length) {
                    allRated = false;
                }
            });

            if (window.commentLength > 0 && window.commentLength < 30) {
                allRated = false;
            }

            $('.review_comment button[type="submit"]').prop("disabled", !allRated);
        }

        // $('#review.modal').on('shown.bs.modal', function() { 
        //     // const postNowBtn = $(".review_comment button[type='submit']");



        //     // Use event delegation to handle dynamically loaded content
        //     $(document).on("change", ".rating-checkbox", validateRatings);

        //     // Ensure validation runs after content is fully loaded
        //     // setTimeout(validateRatings, 500);
        // });

    </script>
    {{-- swal message --}}
    <script>
        @if($message)
        Swal.fire({
            title: 'Error',
            text: '{{ $message }}',
            icon: 'error'
        });

        // make url without message
        var url = window.location.href;
        var newUrl = url.split('?')[0];
        window.history.pushState({}, '', newUrl);
        @endif
    </script>
@endpush
