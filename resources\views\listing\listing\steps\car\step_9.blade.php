@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', 'transmission-type');
@endphp
<fieldset class="select_categories_step min_stay_requirement_step">
    <div class="inner_section_fieldset h_100">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title }}</h2>
                        @isset($step_data->sub_title)
                            <p>{{ $step_data->sub_title }}</p>
                        @endisset
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_categories_main_col inner_section_transmission_main_col scrollable-section">
                    <div class="row">
                        @php
                            $transmissions = [
                                'manual' => [
                                    'icon' => 'manual_transmission.png',
                                    'label' => translate('stepper.manual'),
                                ],
                                'automatic' => [
                                    'icon' => 'automatic_transmission.png',
                                    'label' => translate('stepper.automatic'),
                                ],
                                'cvt' => [
                                    'icon' => 'cvt_transmission.png',
                                    'label' => translate('stepper.cvt'),
                                ],
                                'dct' => [
                                    'icon' => 'dct_transmission.png',
                                    'label' => translate('stepper.dct'),
                                ],
                                'semi_auto' => [
                                    'icon' => 'semi_auto_transmission.png',
                                    'label' => translate('stepper.semi_automatic'),
                                ],
                                'no_transmission' => [
                                    'icon' => 'not_allowed_icon.png',
                                    'label' => translate('stepper.no_transmission'),
                                ],
                                'other' => [
                                    'icon' => 'no_transmission.png',
                                    'label' => translate('stepper.other'),
                                ],
                            ];
                        @endphp

                        @foreach ($transmissions as $transmission_type => $attributes)
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_transmission_col">
                                <div class="inner_section_single_category">
                                    <input type="radio" name="transmission" value="{{ $transmission_type }}"
                                        @if (isset($listing) && $listing->detail->transmission == $transmission_type) checked @endif
                                        id="transmission_{{ $loop->index }}">
                                    <label for="transmission_{{ $loop->index }}">
                                        <div class="category_icon_wrapper">
                                            <img src="{{ asset('website/images/' . $attributes['icon']) }}"
                                                alt="{{ $attributes['label'] }}">
                                        </div>
                                        <div class="category_title">
                                            <h5>{{ $attributes['label'] }}</h5>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @endforeach

                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}"
        disabled />
    <input type="button" name="previous" class="previous action-button-previous"
        value="{{ translate('stepper.back') }}" />
</fieldset>


@push('js')
    <script>
        $(document).ready(function() {

            function listingTransmissionValidation() {
                var checkedTransCount = 0;
                var test = 0;
                $('.select_categories_step .inner_section_transmission_main_col .single_transmission_col').each(
                    function() {
                        if ($(this).find('input[type="radio"]').is(':checked')) {
                            checkedTransCount++;
                        }
                    });
                if (checkedTransCount == 0) {
                    $('.select_categories_step:has(.inner_section_transmission_main_col) .next').prop('disabled',
                        true);
                    console.log('Not Checked Transmissiom');

                } else {
                    $('.select_categories_step:has(.inner_section_transmission_main_col) .next').prop('disabled',
                        false);
                }
            }

            listingTransmissionValidation();

            $(document).on('change',
                '.select_categories_step .inner_section_transmission_main_col .single_transmission_col input[type="radio"]',
                function() {
                    listingTransmissionValidation();
                });
        });
    </script>
@endpush
