@forelse ($conversations as $conversation)
    @php
        $sender = $conversation->sender_id == auth()->id() ? $conversation->receiver : $conversation->sender;
        $userRole = $sender->roles->first()->name ?? 'user';
        $roleClass = '';
        $roleLabel = '';

        switch($userRole) {
            case 'service':
                $roleClass = 'role-provider';
                $roleLabel = translate('chatbox.provider');
                break;
            case 'customer':
                $roleClass = 'role-traveller';
                $roleLabel = translate('chatbox.traveller');
                break;
            case 'sub_admin' && 'user':
                $roleClass = 'role-admin';
                $roleLabel = translate('chatbox.admin');
                break;
            default:
                $roleClass = 'role-user';
                $roleLabel = translate('chatbox.admin');
                break;
        }
    @endphp
    <button class="single_chat_thread conversation-user {{ $conversation->has_unread ? 'has-unread' : '' }} {{ $roleClass }}"
            data-conversation-ids="{{ $conversation->ids }}"
            data-user-ids="{{ $sender->ids }}"
            data-user-role="{{ $userRole }}"
            data-unread-count="{{ $conversation->unread_count }}">
        <div class="user_profile_picture">
            <img src="{{ asset('website') . '/' . $sender->avatar }}" style="border-radius: 50%" alt="User Profile">
            @if ($conversation->has_unread)
                <div class="unread-count-badge">{{ $conversation->unread_count }}</div>
            @endif
        </div>
        <div class="chat_details_wrapper">
            <div class="username_date_wrapper">
                <div class="user_name">
                    <h6 class="{{ $conversation->has_unread ? 'unread-text' : '' }}">
                        {{ $sender->name }}
                        {{-- <small class="user-role-badge {{ $roleClass }}">{{ $roleLabel }}</small> --}}
                    </h6>
                </div>
                <div class="chat_date_unread_wrapper">
                    @if ($conversation->lastMessage)
                        <div class="chat_date">
                            <span class="{{ $conversation->has_unread ? 'unread-text' : '' }}">{{ $conversation->lastMessage->created_at->diffForHumans() }}</span>
                        </div>
                    @endif
                </div>
            </div>
            <div class="chat_preview text-start">
                <p class="{{ $conversation->has_unread ? 'unread-text' : '' }}">
                    @if ($conversation->lastMessage)
                        {{ $conversation->lastMessage->sender_id == auth()->id() ? 'You: ' : '' }}
                        {{ htmlspecialchars_decode($conversation->lastMessage->content ?? 'No message yet', ENT_QUOTES) }}
                    @else
                        {{ translate('chatbox.no_msg_yet') }}
                    @endif
                </p>
                <small class="user-role-badge {{ $roleClass }}">{{ $roleLabel }}</small>
            </div>
        </div>
    </button>
@empty
    <div class="single_chat_thread">
        <div class="chat_details_wrapper">
            <div class="username_date_wrapper">
                <div class="user_name">
                    <h6>{{ translate('chatbox.no_user_found_msg') }}</h6>
                </div>
            </div>
        </div>
    </div>
@endforelse
{{-- Load More Button --}}
@if ($conversations->hasMorePages())
    <button class="load-more-conversation btn btn-warning" data-next-page="{{ $conversations->nextPageUrl() }}">{{ translate('chatbox.load_more_btn') }}</button>
@endif
