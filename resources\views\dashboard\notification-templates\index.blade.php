@extends('layouts.master')

@section('content')
<div class="container-fluid">
    <div class="row">


        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="box-title">Notification Templates</h2>
                <a class="btn btn_yellow" href="{{ route('notification-templates.create') }}">
                    <i class="icon-plus"></i>
                    Create Template
                </a>
            </div>
        </div>



        <div class="col-md-12">
            <div class="white-box card_height_100 mb_30" style="margin-top: 20px;">
                {{-- <div class="white_card_header">
                    <div class="box_header m-0">
                        <div class="main-title">
                            <h2 class="m-0">Notification Templates</h2>
                        </div>
                        <div class="header_more_tool">
                            <a href="{{ route('notification-templates.create') }}" class="btn btn-primary">
                                <i class="fa fa-plus"></i> Create Template
                            </a>
                        </div>
                    </div>
                </div> --}}
                <div class="white_card_body">
                    <div class="table-responsive">
                        <table class="table custom_table">
                            <thead>
                                <tr>
                                    <th>Key</th>
                                    <th>Type</th>
                                    <th>English Title</th>
                                    <th>Spanish Title</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($templates as $template)
                                    <tr>
                                        <td>{{ $template->key }}</td>
                                        <td>
                                            <span class="badge badge-{{ $template->type === 'system' ? 'primary' : 'secondary' }}">
                                                {{ ucfirst($template->type) }}
                                            </span>
                                        </td>
                                        <td>{{ $template->getTranslation('en', 'title') }}</td>
                                        <td>{{ $template->getTranslation('es', 'title') }}</td>
                                        <td>
                                            <span class="badge badge-{{ $template->is_active ? 'success' : 'danger' }}">
                                                {{ $template->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>

                                        {{-- <td>
                                            <a href="{{ route('notification-templates.edit', $template->id) }}" 
                                               class="btn btn-sm btn-warning">
                                                <i class="fa fa-edit"></i>
                                            </a>
                                            @if($template->type !== 'system')
                                                <form action="{{ route('notification-templates.destroy', $template->id) }}" 
                                                      method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger" 
                                                            onclick="return confirm('Are you sure?')">
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </td> --}}



                                        <td class="form_btn ">
                                            <div class="dropdown">
                                                <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                                                </button>
                                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                    <a href="{{ route('notification-templates.edit', $template->id) }}" class="dropdown-item">
                                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                                        Edit
                                                    </a>
                                                    @if($template->type !== 'system')
                                                        <form action="{{ route('notification-templates.destroy', $template->id) }}" 
                                                            method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="dropdown-item btn-sm delete-btn" 
                                                                    onclick="return confirm('Are you sure?')">
                                                                <i class="fa fa-trash"></i>
                                                                Delete
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </div>

                                        </td>



                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No templates found</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection