@extends('layouts.master')
@push('css')
    <!-- <link href="{{ asset('plugins/components/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css" /> -->
    <!-- <link href="https://cdn.datatables.net/buttons/1.2.2/css/buttons.dataTables.min.css" rel="stylesheet" type="text/css" /> -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="{{ asset('plugins/components/morrisjs/morris.css') }}" rel="stylesheet">

    <style>


    </style>
@endpush
@section('content')
    @if (auth()->user()->hasRole('service'))
        <section class="card_sect pb-2">
            <div class="container-fluid">
                <div class="row main_cart_sec">
                    <div class="col-md-12">
                        <h1>{{ translate('dashboard_ewallet.your') }} {{ translate('dashboard_ewallet.e_wallet') }}</h1>
                        <div class="card_wrapper">
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.total_earning') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($total_earning ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small> {{ number_format($total_earning_usd, 2) }} USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill">
                                            <h1>E</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.total_platform_fee') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($wallet->commission_amount ?? 0, 0, '.', ',') }} COP</h1>
                                        <small>
                                            {{ number_format(($wallet->commission_amount ?? 0) * ($usd_conversion_rate ?? 1), 2) }}
                                            USD</small>

                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1>F</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.incoming_payments') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($pending_withdrawal ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small> {{ number_format($pending_withdrawal_usd ?? 0, 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill">
                                            <h1>I</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.available_withdrawl') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($wallet->amount ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small>
                                            {{ number_format(($wallet->amount ?? 0) * ($usd_conversion_rate ?? 1), 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1>A</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.in_transit') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($in_process ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small> {{ number_format($in_process_usd ?? 0, 2) }} USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill ">
                                            <h1>T</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ trans('in_process') }}</p>
                                        <h1 class="semiBold_fontweight">$1500</h1>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1>W</h1>
                                        </div>
                                    </div>
                                </div>
                            </div> --}}
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.total_cancellation') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($total_cancellation ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        </h1>
                                        <small> {{ number_format($total_cancellation_usd ?? 0, 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill black_card_pill ">
                                            <h1>C</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.adjusments') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($adjustments ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        </h1>
                                        <small> {{ number_format($adjustments_usd ?? 0, 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill">
                                            <h1>A</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @elseif (auth()->user()->hasRole(['user', 'sub_admin']))
        <section class="card_sect pb-2">
            <div class="container-fluid">
                <div class="row main_cart_sec">
                    <div class="col-md-12">
                        <h1>{{ translate('dashboard_ewallet.your') }} {{ translate('dashboard_ewallet.e_wallet') }}</h1>
                        <div class="card_wrapper">
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.total_earning') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($total_earning ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small> {{ number_format($total_earning_usd ?? 0, 2) }} USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill">
                                            <h1>E</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.total_platform_fee') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($wallet->commission_amount ?? 0, 0) }} COP</h1>
                                        <small>
                                            {{ number_format(($wallet->commission_amount ?? 0) * ($usd_conversion_rate ?? 1), 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1>F</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.incoming_payments') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($pending_withdrawal ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small> {{ number_format($pending_withdrawal_usd ?? 0, 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill">
                                            <h1>I</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.available_withdrawl') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($wallet->amount ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small>
                                            {{ number_format(($wallet->amount ?? 0) * ($usd_conversion_rate ?? 1), 2) }}
                                            USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="card_pill black_card_pill">
                                            <h1>A</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.in_transit') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($in_process ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        <small> {{ number_format($in_process_usd ?? 0, 2) }} USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill ">
                                            <h1>T</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{-- <div class="card_element">
                            <div class="card_parent white_card">
                                <div class="card_inners">
                                    <p>{{ trans('in_process') }}</p>
                                    <h1 class="semiBold_fontweight">$1500</h1>
                                </div>
                                <div class="card_inners">
                                    <div class="card_pill black_card_pill">
                                        <h1>W</h1>
                                    </div>
                                </div>
                            </div>
                        </div> --}}
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.total_cancellation') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($total_cancellation ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        </h1>
                                        <small> {{ number_format($total_cancellation_usd ?? 0, 2) }} USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill black_card_pill ">
                                            <h1>C</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card_element">
                                <div class="card_parent white_card">
                                    <div class="card_inners">
                                        <p>{{ translate('dashboard_ewallet.adjusments') }}</p>
                                        <h1 class="notranslate semiBold_fontweight">
                                            ${{ number_format($adjustments ?? 0, 0, '.', ',') }} COP
                                        </h1>
                                        </h1>
                                        <small> {{ number_format($adjustments_usd ?? 0, 2) }} USD</small>
                                    </div>
                                    <div class="card_inners">
                                        <div class="notranslate card_pill yellow_card_pill">
                                            <h1>A</h1>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endif
    {{-- <section class="btn-row ">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="btn-group ">
                        <button class="btn btn-secondary btn-lg dropdown-toggle btn_yellow" type="button"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="margin-top: 10px;">
                            {{ trans('filter') }}
                            <span><i class="fa-solid fa-filter"></i></span>
                        </button>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">{{ trans('this_month') }}</a>
                            <a class="dropdown-item" href="#">{{ trans('this_year') }}</a>
                            <a class="dropdown-item" href="#">{{ trans('this_week') }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> --}}
    <section class="chart-sec">
        <div class="container-fluid">
            <div class="row p-0">
                <div class="col-md-12">
                    <div id="accordion" class="white-box ">
                        <div class="accordion-item">
                            <div class="card-header py-2" id="earing_insigts_head">
                                <h5 class="m-0">
                                    <a class="box-titile accordion_heading d-flex justify-content-between"
                                        data-toggle="collapse" data-target="#earing_insigts" aria-expanded="true"
                                        aria-controls="earing_insigts">
                                        <span>
                                            {{ translate('dashboard_ewallet.earning_insights') }}
                                        </span>
                                        <span class="acc_icon"><i class="fa fa-angle-down"></i></span>
                                    </a>
                                </h5>
                            </div>
                            <div id="earing_insigts" class="accordion-body collapse in pt-2"
                                aria-labelledby="earing_insigts" data-parent="#accordion">
                                {{-- <div class="btn-group trans ">
                                    <button class="btn btn-secondary btn-lg dropdown-toggle" type="button"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {{ trans('this_week') }} <span><i class="fa-solid fa-angle-down ms-1"></i></span>
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item filter-option" data-filter="today" href="#"> {{ trans('today') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="this_week" href="#"> {{ trans('this_week') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="last_month" href="#">{{ trans('last_month') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="last_year" href="#">{{ trans('last_year') }}</a>
                                    </div>
                                </div> --}}
                                <div class="btn-group trans earning_filter">
                                    <button class="btn btn-secondary btn-lg dropdown-toggle" type="button"
                                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"
                                        id="selected-filter">
                                        {{ translate('dashboard_ewallet.lifetime') }} <span><i
                                                class="fa-solid fa-angle-down ms-1"></i></span>
                                    </button>
                                    <div class="dropdown-menu">

                                        <a class="dropdown-item filter-option" data-filter="today"
                                            href="javascript:void(0);">
                                            {{ translate('dashboard_ewallet.today') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="this_week"
                                            href="javascript:void(0);">
                                            {{ translate('dashboard_ewallet.this_week') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="last_month"
                                            href="javascript:void(0);">{{ translate('dashboard_ewallet.last_month') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="last_year"
                                            href="javascript:void(0);">{{ translate('dashboard_ewallet.last_year') }}</a>
                                        <a class="dropdown-item filter-option" data-filter="overall"
                                            href="javascript:void(0);">
                                            {{ translate('dashboard_ewallet.lifetime') }}</a>
                                        {{-- <a class="dropdown-item filter-option" data-filter="lifetime"
                                            href="javascript:void(0);">{{ translate('dashboard_ewallet.lifetime') }}</a> --}}
                                    </div>
                                </div>
                                <div class="row">
                                    {{-- <div id="morris-line-chart"></div> --}}
                                    <div class="col-md-6 earning_parent">
                                        {{-- <div id="earning" class="text-center"></div> --}}
                                        <div class="text-center">
                                            <canvas id="earningChart"></canvas>
                                        </div>
                                    </div>
                                    <div class="col-md-6 earning1_parent">
                                        {{-- <div id="earning1" class="text-center"></div> --}}
                                        <div class="text-center">
                                            <canvas id="earningChart1"></canvas>
                                        </div>
                                    </div>
                                    {{-- <div class="col-md-5 earning_chart1">
                                        <div class="earning_key_wrapper pt-3">
                                            <ul class="earning_key d-flex gap-4 ms-1 align-items-center">
                                                <li>
                                                    <span class="key"></span><span class="key_value">Total
                                                        Earning</span>
                                                </li>
                                                <li>
                                                    <span class="key"></span><span class="key_value">Total
                                                        Cancellation</span>
                                                </li>
                                                <li>
                                                    <span class="key"></span><span class="key_value">Adjusments</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="col-md-7 earning_chart2">
                                        <div class="earning_key_wrapper pt-3">
                                            <ul
                                                class="earning_key2 d-flex gap-2 justify-content-between align-items-center">
                                                <li>
                                                    <span class="key"></span><span class="key_value">Fees Paid</span>
                                                </li>
                                                <li>
                                                    <span class="key"></span><span class="key_value">Outstanding
                                                        Payments</span>
                                                </li>
                                                <li>
                                                    <span class="key"></span><span class="key_value">Incoming
                                                        Withdrawal</span>
                                                </li>
                                                <li>
                                                    <span class="key"></span><span class="key_value">In Transit</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div> --}}
                                    <div class="col-md-12">
                                        <h3 class="no-earning text-center d-none">
                                            {{ translate('dashboard_ewallet.no_earning') }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="payment-sec tab_fixed  tabs_sec">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="white-box m-0">
                        <div class="d-flex justify-content-between">
                            <h1> {{ translate('dashboard_ewallet.manage_payouts') }}</h1>
                            <ul class="nav nav-pill d-flex gap-1 align-items-center">
                                <li><a class="btn_yellow" data-toggle="pill"
                                        href="#payments">{{ translate('dashboard_ewallet.configure_payments') }}</a>
                                </li>
                                <li> <a data-toggle="modal" data-target="#autoSchedule" href="javascript:void(0);"
                                        class="btn_yellow auto">
                                        {{ translate('dashboard_ewallet.auto_payout') }}
                                    </a>
                                </li>
                                <li><a data-toggle="modal" data-target="#request_wd" href="javascript:void(0);"
                                        class="btn_yellow manual">{{ translate('dashboard_ewallet.manual_payout') }}</a>
                                </li>
                                <li>
                                    <a data-toggle="pill"
                                        href="#Widthdrawal">{{ translate('dashboard_ewallet.withdrawal_request') }}</a>
                                </li>
                                <li><a data-toggle="pill"
                                        href="#Payments">{{ translate('dashboard_ewallet.payments') }}</a></li>
                            </ul>
                        </div>
                        <div class="tab-content m-0">
                            <div id="payments" class="tab-pane fade">
                                <iframe src="{{ $iframeUrl ?? '' }}" width="100%" height="850px"
                                    style="border: none;">
                                    {{ translate('dashboard_ewallet.browser_not_support_iframe') }}.
                                </iframe>
                            </div>
                            <div id="Widthdrawal" class="tab-pane fade">
                                <h3>{{ translate('dashboard_ewallet.withdrawal_requests') }}</h3>
                                <div class="table-responsive">
                                    <table id="myTable" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col" style="width: 130px;">
                                                    {{ translate('dashboard_ewallet.profile_name') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.date') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.amount') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.currency') }}</th>
                                                <th scope="col" style="width: 120px;">{{ translate('dashboard_ewallet.payment_method') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.status') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($withdrawal_requests->filter(function($wr) {
                                                                            return $wr->status != 4;
                                                                        }) as $withdrawal_request)
                                                <tr>
                                                    <td>{{ $withdrawal_request->user->name ?? '-' }}</td>
                                                    <td>{{ $withdrawal_request->created_at->setTimezone('America/Bogota')->format('d-M-Y h:i:A') }}</td>
                                                    <td>{{ number_format($withdrawal_request->amount, 0, '.', ',') }}</td>
                                                    <td>{{ $withdrawal_request->currency ?? '-' }}</td>
                                                    <td>{{ $withdrawal_request->payment_method_type ?? 'N/A' }}</td>
                                                    <td
                                                        @if ($withdrawal_request->status == 0) class="text-danger" 
                                                        @elseif ($withdrawal_request->status == 5) 
                                                            class="text-warning" 
                                                        @else 
                                                            class="text-success" @endif>
                                                        @if ($withdrawal_request->status == 0)
                                                            In Transit
                                                        @elseif ($withdrawal_request->status == 5)
                                                            Error
                                                        @else
                                                            Paid
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center">
                                                        <h3>
                                                            {{ translate('dashboard_ewallet.no_withdrawal_request_yet') }}
                                                        </h3>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div id="Payments" class="tab-pane fade">
                                <h3>{{ translate('dashboard_ewallet.payments') }} </h3>
                                <div class="table-responsive">
                                    <table id="myTable" class="table  custom_table">
                                        <thead>
                                            <tr>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.profile_name') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.submission') }} {{ trans('date') }}
                                                </th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.amount') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.currency') }}</th>
                                                <th scope="col" style="width: 120px;">{{ translate('dashboard_ewallet.payment_method') }}</th>
                                                <th scope="col" style="width: 120px;">
                                                    {{ translate('dashboard_ewallet.status') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($withdrawal_requests->filter(function($wr) { return $wr->status == 4; }) as $withdrawal_request)
                                                <tr>
                                                    <td>{{ $withdrawal_request->user->name ?? '-' }}</td>
                                                    <td>{{ $withdrawal_request->submission_date ? \Carbon\Carbon::parse($withdrawal_request->submission_date)->setTimezone('America/Bogota')->format('d-M-Y h:i:A') : 'N/A' }}
                                                    </td>
                                                    <td>{{ number_format($withdrawal_request->amount, 0, '.', ',') }}</td>
                                                    <td>{{ $withdrawal_request->currency ?? '-' }}</td>
                                                    <td>{{ $withdrawal_request->payment_method_type ?? 'N/A' }}</td>
                                                    <td class="text-success">
                                                        Paid
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center">
                                                        <h3>
                                                            {{ translate('dashboard_ewallet.no_payments') }}
                                                        </h3>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {{-- <section class="table_sect tab_fixed">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="table_wrraper">
                        <ul class="nav nav-pills">
                            
                        </ul>
                        <div class="tab-content">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section> --}}
    {{-- Modal --}}
    <section class=" reject">
        {{-- <div class="modal fade" id="rejModal" tabindex="-1" role="dialog">
            <div class="modal-dialog dialog-centered" id="myModal" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <span class="close" data-dismiss="modal">&times;</span>
                        <h1 class="modal-title" id="exampleModalLabel1">{{ trans('reject') }}
                        </h1>
                        <div class="form_field_padding">
                            <div class="mb-3">
                                <textarea class="form-control" name="" id="" rows="10" column="25"
                                    placeholder="Type Message"></textarea>
                            </div>
                        </div>
                        <div class=" modal_btn">
                            <button type="button" class="btn yellow">{{ trans('send') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}
        <div class="modal fade" id="request_wd" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        {{-- <span class="close" data-dismiss="modal">&times;</span> --}}
                        <h1 class="modal-title" id="exampleModalLabel1">
                            {{ translate('dashboard_ewallet.manual_payout') }}</h1>
                        <form method="POST" id="withdraw-form">
                            @csrf
                            <fieldset>
                                <div id="wallet-up-error" style="display: none" class="alert alert-danger text-left"
                                    role="alert">
                                    <ul>
                                    </ul>
                                </div>
                                <div class="mb-2 form_field_padding">
                                    <label for="wd_amount">{{ translate('dashboard_ewallet.withdraw_heading') }}</label>
                                    <div class="input-group">
                                        <input type="text" min="0" class="form-control cop" name="amount"
                                            id="wd_amount"
                                            placeholder="{{ translate('dashboard_ewallet.enter_amount') }}">
                                        <div class="input-group-addon">COP</div>
                                    </div>
                                    <input type="hidden" name="wallet_user_email" id="wallet_user_email"
                                        value="{{ auth()->user()->email ?? '' }}" />
                                </div>
                                <button type="button" class="btn_yellow mb-4 d-block w-100" style="margin-top: 15px;"
                                    id="wallet-request-btn">{{ translate('dashboard_ewallet.submit') }}</button>
                            </fieldset>
                            {{-- <button type="submit" class="btn yellow">{{ trans('send') }} </button> --}}
                            <fieldset style="display: none;">
                                <h4 class="modal-title text-center fs-18 text-black">
                                    {{ translate('dashboard_ewallet.verify_heading') }}</h4>
                                <p class="fs-14 text-center">{{ translate('dashboard_ewallet.verify_message') }} <span
                                        class="fw-bold user-email-span"></span></p>
                                <div class="form-group form_field_padding">
                                    <input type="text" class="form-control code_otp" id="manual_otp_inpt"
                                        placeholder="{{ translate('dashboard_ewallet.enter_verification_code') }}">
                                </div>

                                <div class="d-flex justify-content-end align-items-center mb-3">

                                    <button type="button" class="trans_btn text-primary mx-auto resend-email-otp-btn"
                                    id="resend-email-otp-btn-manual">{{ translate('dashboard_ewallet.resend') }}
                                    {{ translate('dashboard_ewallet.code') }}</button>

                                    <span id="phone_otp_timer_manual" class="text-muted"></span>
                                </div>

                                <input type="button" name="next" id="manual_verify_otp_btn"
                                    class="btn_yellow mb-4 w-100 d-inline-block mb-3 disable-on-click" value="Verify" />
                                
                            </fieldset>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {{-- ----- --}}
        {{-- ----- --}}
        {{-- wallet scheduler Modal --}}
        @include('dashboard.wallet_scheduler')
    </section>
@endsection
@push('js')
    {{-- <script src="{{ asset('js/db1.js') }}"></script> --}}
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>
    <script src="{{ asset('plugins/components/raphael/raphael-min.js') }}"></script>
    {{-- <script src="{{ asset('plugins/components/morrisjs/morris.js') }}"></script> --}}
    {{-- <script src="{{ asset('js/morris-data.js') }}"></script> --}}
    <script src="{{ asset('plugins/components/sparkline/jquery.sparkline.min.js') }}"></script>
    {{-- <script src="{{ asset('plugins/components/custom-chart/chart.js') }}"></script> --}}
    {{-- <script src="{{ asset('plugins/components/chartist-js/dist/chartist.js') }}"></script> --}}

    <script src="{{ asset('plugins/components/custom-chart/chart.min.js') }}"></script>
    <script src="{{ asset('plugins/components/custom-chart/chart-js-plugin-label.js') }}"></script>

    <script>
        var sparklineLogin = function() {
            var total_earning = "{{ $total_earning ?? 0 }}";
            var total_cancellation = "{{ $total_cancellation ?? 0 }}";
            var adjustments = "{{ $adjustments ?? 0 }}";
            var in_process = "{{ $in_process ?? 0 }}";
            var platform_fee = "{{ $wallet->commission_amount ?? 0 }}";
            var outstanding_payments = "{{ $wallet->amount ?? 0 }}";
            var pending_withdrawl = "{{ $pending_withdrawal ?? 0 }}";
            $('#earning').sparkline([total_earning, total_cancellation, adjustments], {
                type: 'pie',
                height: '250',
                resize: true,
                sliceColors: ['#A2DE5B', '#e65862', '#2f2e2e5a'],
            });
            if (total_earning == 0 && total_cancellation == 0 && adjustments == 0) {
                $('.earning_parent').addClass('d-none');
                $('.earning_chart1').addClass('d-none');
            }
            if (in_process == 0 && platform_fee == 0 && outstanding_payments == 0 && pending_withdrawl == 0) {
                $('.earning1_parent').addClass('d-none');
                $('.earning_chart2').addClass('d-none');
            }
            if (in_process == 0 && platform_fee == 0 && outstanding_payments == 0 && pending_withdrawl == 0 &&
                total_earning == 0 && total_cancellation == 0 && adjustments == 0) {
                $('.earning_filter').addClass('d-none');
                $('#earing_insigts').find('.no-earning').removeClass('d-none');
            }

            $('#earning1').sparkline([platform_fee, outstanding_payments, pending_withdrawl, in_process], {
                type: 'pie',
                height: '250',
                resize: true,
                sliceColors: ['#D16DFF', '#446CB0', '#ff7932', '#ffce32'],
            });
        }
        var sparkResize;
        $(window).resize(function(e) {
            clearTimeout(sparkResize);
            sparkResize = setTimeout(sparklineLogin, 500);
        });

        sparklineLogin();
    </script>

    <script>
        // Optional: Add a small visual indicator when code is being resent
        $(document).on('click', '#resend-email-otp-btn', function() {
            if (!$(this).prop('disabled')) {
                // Add a subtle animation to the OTP input to draw attention
                $('#verify_otp_btn').addClass('animate__animated animate__pulse');
                setTimeout(() => {
                    $('#verify_otp_btn').removeClass('animate__animated animate__pulse');
                }, 1000);
            }
        });

        $(document).on('click', '#resend-email-otp-btn-manual', function() {
            if (!$(this).prop('disabled')) {
                // Add a subtle animation to the OTP input to draw attention
                $('#manual_verify_otp_btn').addClass('animate__animated animate__pulse');
                setTimeout(() => {
                    $('#manual_verify_otp_btn').removeClass('animate__animated animate__pulse');
                }, 1000);
            }
        });

        let otpTimers = {};

        // Start OTP timer for a specific modal
        function startPhoneOtpTimer(timerId, buttonId) {
            let timeLeft = 30; // 15 minutes in seconds
            const $timer = $(timerId);
            const $resendBtn = $(buttonId);

            // Clear any existing timer for this modal
            if (otpTimers[timerId]) {
                clearInterval(otpTimers[timerId]);
            }

            $resendBtn.prop("disabled", true).hide();

            otpTimers[timerId] = setInterval(function () {
                if (timeLeft <= 0) {
                    clearInterval(otpTimers[timerId]);
                    otpTimers[timerId] = null;
                    $timer.text("");
                    $resendBtn.prop("disabled", false).show();
                } else {
                    const minutes = Math.floor(timeLeft / 60);
                    const seconds = timeLeft % 60;
                    const formattedTime =
                        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    $timer.text(`Resend in ${formattedTime}`);
                    timeLeft--;
                }
            }, 1000);
        }

        // Clear OTP timer for a specific modal
        function clearPhoneOtpTimer(timerId, buttonId) {
            if (otpTimers[timerId]) {
                clearInterval(otpTimers[timerId]);
                otpTimers[timerId] = null;
                $(timerId).text("");
                $(buttonId).prop("disabled", false).show();
            }
        }


    </script>

    <script>
        // $(document).on('click', '.filter-option', function() {
        //     var filter = $(this).data('filter');
        //     var filterText = $(this).text();
        //     $('#selected-filter').html(filterText + ' <span><i class="fa-solid fa-angle-down ms-1"></i></span>');

        //     // Make the AJAX request to the server
        //     $.ajax({
        //         url: "{{ route('get-earning-data') }}", // Change this to your route
        //         method: "GET",
        //         data: {
        //             filter: filter
        //         },
        //         success: function(response) {
        //             // After receiving the data, update the chart
        //             updateCharts(response);
        //         }
        //     });
        // });
        // function updateCharts(data) {
        //     $('#earning').sparkline([data.total_earning, data.total_cancellation, data.adjustments], {
        //         type: 'pie',
        //         height: '250',
        //         resize: true,
        //         sliceColors: ['#A2DE5B', '#e65862', '#2f2e2e5a']
        //     });
        //     $('#earning1').sparkline([data.platform_fee, data.outstanding_payments, data.pending_withdrawl, data
        //         .in_process
        //     ], {
        //         type: 'pie',
        //         height: '250',
        //         resize: true,
        //         sliceColors: ['#D16DFF', '#446CB0', '#ff7932', '#ffce32']
        //     });
        // }
        // $(document).ready(function() {
        //     var defaultFilter = 'overall';
        //     $.ajax({
        //         url: "{{ route('get-earning-data') }}",
        //         method: "GET",
        //         data: {
        //             filter: defaultFilter
        //         },
        //         success: function(response) {
        //             updateCharts(response);
        //         }
        //     });
        // });
    </script>
    <script>
        $("#signup-btn").on("click", function(e) {
            e.preventDefault();
            let sign_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">Loading...</span></div>`
            ).prop('disabled', true);
            let signup_data = $("#signUp-form").serialize();
            let user_email = $("#user_email").val();
            $.ajax({
                url: "{{ route('wallet_scheduler') }}",
                data: signup_data,
                type: "POST",
                success: function(response) {
                    if (response.status == true) {
                        $("#sign-up-error").slideUp(); // Hide any previous errors
                        $("#signup-btn").addClass("next"); // Add 'next' class to button
                        $(".user-email-span").html(user_email); // Update the user email in the view

                        setTimeout(() => {
                            // Reset the button after the successful response
                            sign_btn.html("<b>Next</b>").removeAttr("id");
                            current_fs = sign_btn.parent().hide();
                            next_fs = sign_btn.parent().next();
                            // Animate the transition to the next fieldset
                            next_fs.show();
                            current_fs.animate({
                                opacity: 0
                            }, {
                                step: function(now) {
                                    opacity = 1 - now;
                                    current_fs.css({
                                        'display': 'none',
                                        'position': 'relative'
                                    });
                                    next_fs.css({
                                        'opacity': opacity
                                    });
                                },
                                duration: 500
                            });
                            startPhoneOtpTimer();
                            startPhoneOtpTimer("#phone_otp_timer", "#resend-email-otp-btn");
                        }, 50);
                    } else {
                        // Handle validation errors
                        $('input').removeClass('is-invalid'); // Remove invalid class from all inputs
                        $("#sign-up-error").slideDown(); // Show error section
                        $("#sign-up-error ul").empty(); // Empty the previous error list
                        // Loop through each error message and append it
                        $.each(response.message, function(key, value) {
                            $("#sign-up-error ul").append(`<li>${value.join('<br>')}</li>`);
                            $('input[name="' + key + '"]').addClass(
                                'is-invalid'); // Add invalid class to the field
                        });
                        // Hide the error section after 9 seconds
                        setTimeout(() => {
                            $("#sign-up-error").slideUp();
                        }, 9000);
                    }
                },
                complete: function() {
                    // Reset the button text and enable it after AJAX call is completed
                    sign_btn.html("{{ translate('dashboard_ewallet.submit') }}").prop('disabled', false);
                }
            });

            function email_verify_submit(email) {
                let otp = $("#otp_inpt").val();
                $.ajax({
                    url: "{{ route('WalletVerification') }}",
                    type: "POST",
                    data: {
                        email: "{{ auth()->user()->email }}",
                        otp,
                        type: "email",
                        status: 1
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#otp-submit-btn-email").show();
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'success'
                            // })
                            window.updateScheduleStatus($("#schedule_active_toggle"));
                            Swal.fire({
                                title: @json(translate('dashboard_ewallet.otp_success_title')),
                                text: @json(translate('dashboard_ewallet.otp_success_message')),
                                icon: "success"
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.reload();
                                }
                            });
                        } else {
                            Swal.fire({
                                title: @json(translate('dashboard_ewallet.error')),
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    complete: function(response) {
                        $(".code_otp input").prop("disabled", false);
                    }
                });
            }
            $(document).on("click", "#verify_otp_btn", function() {
                let email = $("#user_email").val();
                if (email != "") {
                    email_verify_submit(email);
                }
            })
            // resend otp
            $(document).on("click", "#resend-email-otp-btn", function() {
                email = $("#user_email").val();
                let resend_otp_btn = $(this).html(
                    `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('dashboard_ewallet.loading') }}</span></div>`
                ).prop('disabled', true);
                if (email) {
                    $.ajax({
                        url: "{{ route('resend_otp') }}",
                        type: "POST",
                        data: {
                            email,
                            type: "email",
                        },
                        success: function(response) {
                            resend_otp_btn.html(
                                "{{ translate('dashboard_ewallet.resend_code') }}")
                            if (response.status == true) {
                                // $.toast({
                                //     heading: 'Success',
                                //     text: response.message,
                                //     showHideTransition: 'plain',
                                //     icon: 'success'
                                // })
                                startPhoneOtpTimer();

                                Swal.fire({
                                    title: @json(translate('dashboard_ewallet.success')),
                                    text: response.message,
                                    icon: 'success',
                                    timer: 1500,
                                    showConfirmButton: false
                                });

                            } else {
                                Swal.fire({
                                    title: @json(translate('dashboard_ewallet.error')),
                                    text: response.message,
                                    icon: 'error',
                                    showConfirmButton: false,
                                    timer: 3000
                                });
                            }
                        },
                        complete: function() {
                            setTimeout(() => {
                                resend_otp_btn.prop('disabled', false);
                            }, 1000 * 60);
                        }
                    });
                }
            });
            // resend otp end
        })
    </script>
    <script>
        $("#wallet-request-btn").on("click", function(e) {
            e.preventDefault();
            let sign_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('dashboard_ewallet.loading') }}</span></div>`
            ).prop('disabled', true);
            let wallet_request_data = $("#withdraw-form").serialize();
            let user_email = $("#wallet_user_email").val();
            $.ajax({
                url: "{{ route('wallet_manual') }}",
                data: wallet_request_data,
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.status == true) {
                        $("#wallet-up-error").slideUp();
                        $(".user-email-span").html(user_email);
                        let current_fs = $(sign_btn).closest('fieldset');
                        let next_fs = current_fs.next('fieldset');
                        $(sign_btn).html("{{ trans('send') }}").prop('disabled', false);
                        current_fs.slideUp(500, function() {
                            next_fs.slideDown(500);
                        });
                        startPhoneOtpTimer("#phone_otp_timer_manual", "#resend-email-otp-btn-manual");
                    } else {
                        $('input').removeClass('is-invalid');
                        $("#wallet-up-error").slideDown();
                        $("#wallet-up-error ul").empty();
                        $.each(response.message, function(key, value) {
                            $("#wallet-up-error ul").append(`<li>${value.join('<br>')}</li>`);
                            $('input[name="' + key + '"]').addClass(
                                'is-invalid');
                        });
                        setTimeout(() => {
                            $("#wallet-up-error").slideUp();
                        }, 9000);
                    }
                },
                complete: function() {
                    sign_btn.html("{{ translate('dashboard_ewallet.submit') }}").prop('disabled', false);
                }
            });

            function manual_email_verify_submit(email) {
                let otp = $("#manual_otp_inpt").val();
                $.ajax({
                    url: "{{ route('WalletVerification') }}",
                    type: "POST",
                    data: {
                        email: "{{ auth()->user()->email }}",
                        otp,
                        type: "email",
                        status: 0
                    },
                    success: function(response) {
                        if (response.status == true) {
                            $("#otp-submit-btn-email").show();
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'success'
                            // })
                            Swal.fire({
                                title: @json(translate('dashboard_ewallet.otp_success_title')),
                                text: @json(translate('dashboard_ewallet.otp_success_message')),
                                icon: "success"
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.reload();
                                }
                            });
                        } else {
                            Swal.fire({
                                title: @json(translate('dashboard_ewallet.error')),
                                text: response.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    complete: function(response) {
                        // Re-enable the button
                        $("#manual_verify_otp_btn").prop("disabled", false);
                        $(".code_otp input").prop("disabled", false);
                    }
                });
            }
            let debounceTimeout;
            $(document).on("click", "#manual_verify_otp_btn", function() {
                if ($(this).prop("disabled")) return;
                let email = $("#wallet_user_email").val();
                if (email != "") {
                    // Disable the button to prevent multiple clicks
                    $(this).prop("disabled", true);
                    clearTimeout(debounceTimeout); // Clear previous timeout to avoid multiple requests
                    debounceTimeout = setTimeout(() => {
                        manual_email_verify_submit(email);
                    }, 900);
                }
            })
            // resend otp
            // $(document).on("click", "#resend-email-otp-btn", function() {
            //     email = $("#user_email").val();
            //     let resend_otp_btn = $(this).html(
            //         `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('dashboard_ewallet.loading') }}</span></div>`
            //     ).prop('disabled', true);
            //     if (email) {
            //         $.ajax({
            //             url: "{{ route('resend_otp') }}",
            //             type: "POST",
            //             data: {
            //                 email,
            //                 type: "email",
            //             },
            //             success: function(response) {
            //                 resend_otp_btn.html(
            //                     "{{ translate('dashboard_ewallet.resend_code') }}")
            //                 if (response.status == true) {
            //                     // $.toast({
            //                     //     heading: 'Success',
            //                     //     text: response.message,
            //                     //     showHideTransition: 'plain',
            //                     //     icon: 'success'
            //                     // })
            //                     Swal.fire({
            //                         title: @json(translate('dashboard_ewallet.success')),
            //                         text: response.message,
            //                         icon: 'success',
            //                         timer: 1500,
            //                         showConfirmButton: false
            //                     });
            //                 } else {
            //                     Swal.fire({
            //                         title: @json(translate('dashboard_ewallet.error')),
            //                         text: response.message,
            //                         icon: 'error',
            //                         showConfirmButton: false,
            //                         timer: 3000
            //                     });
            //                 }
            //             },
            //             complete: function() {
            //                 setTimeout(() => {
            //                     resend_otp_btn.prop('disabled', false);
            //                 }, 1000 * 60);
            //             }
            //         });
            //     }
            // });
            // resend otp end
        })
    </script>
    <script>
        $(document).ready(function() {
            // $('.cop').on('input', function() {
            //     let value = $(this).val();
            //     value = value.replace(/[^\d]/g, '');
            //     formatValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            //     $(this).val(formatValue);
            // });
            // Update hidden input when toggle changes



            // $("#schedule_active_toggle").change(function() {

            window.updateScheduleStatus = function($toggler) {

                
                let status = $toggler.is(":checked") ? 1 : 0;
                $("#schedule_active_hidden").val(status);
                // Store reference to the toggle
                let $toggle = $toggler;
                $.ajax({
                    url: "{{ route('update_schedule_status') }}",
                    type: "POST",
                    data: {
                        schedule_active: status,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function(response) {
                        if (response.status) {
                            // $.toast({
                            //     heading: @json(translate('dashboard_ewallet.toast_success')),
                            //     text: @json(translate('dashboard_ewallet.payout_status_updated')),
                            //     showHideTransition: 'plain',
                            //     icon: 'success'
                            // });

                            // Swal.fire({
                            //     title: @json(translate('dashboard_ewallet.toast_success')),
                            //     text: @json(translate('dashboard_ewallet.payout_status_updated')),
                            //     icon: 'success',
                            //     timer: 1500,
                            //     showConfirmButton: false
                            // });

                        } else {
                            // $.toast({
                            //     heading: 'Error',
                            //     text: response.message || 'Failed to update status',
                            //     showHideTransition: 'plain',
                            //     icon: 'error'
                            // });
                            Swal.fire({
                                title: @json(translate('dashboard_ewallet.error')),
                                text: response.message || 'Failed to update status',
                                icon: "error",
                                timer: 1500,
                            });
                            // Revert toggle to previous state if update failed
                            $toggle.prop('checked', !status);
                            $("#schedule_active_hidden").val(!status);
                        }
                    },
                    error: function(xhr) {
                        // $.toast({
                        //     heading: 'Error',
                        //     text: 'Server error occurred',
                        //     showHideTransition: 'plain',
                        //     icon: 'error'
                        // });
                        Swal.fire({
                            title: @json(translate('dashboard_ewallet.error')),
                            text: 'Server error occurred',
                            icon: "error",
                            timer: 1500,
                        });
                        // Revert toggle to previous state on error
                        $toggle.prop('checked', !status);
                        $("#schedule_active_hidden").val(!status);
                    }
                });

            }
            // });

            // $(document).on("click", "#signup-btn", function () {
            //     setTimeout(() => {
            //         startPhoneOtpTimer("#phone_otp_timer", "#resend-email-otp-btn");
            //     }, 1000);
            // });

            // $(document).on("click", "#wallet-request-btn", function () {
            //     setTimeout(() => {
            //         startPhoneOtpTimer("#phone_otp_timer_manual", "#resend-email-otp-btn-manual");
            //     }, 1000);
            // });

            // Resend OTP click → Restart timer
            $(document).on("click", "#resend-email-otp-btn", function () {
                startPhoneOtpTimer("#phone_otp_timer", "#resend-email-otp-btn");
            });

            $(document).on("click", "#resend-email-otp-btn-manual", function () {
                startPhoneOtpTimer("#phone_otp_timer_manual", "#resend-email-otp-btn-manual");
            });



            // var savedState = $('#schedule_active_hidden').val();

            // $('#autoSchedule').on('shown.bs.modal', function () {
            //     $('#schedule_active_toggle').prop('checked', savedState === '1');
            // });

            // $('#autoSchedule').on('hidden.bs.modal', function () {
            //     $('#schedule_active_toggle').prop('checked', savedState === '1');
            // });


            var savedState = $('#schedule_active_hidden').val();

            var originalValues = {};

            $('#autoSchedule').on('shown.bs.modal', function () {
                $('#schedule_active_toggle').prop('checked', savedState === '1');

                $('#autoSchedule input[type="text"]').each(function () {
                    var id = $(this).attr('id');
                    originalValues[id] = $(this).val();
                });
            });

            $('#autoSchedule').on('hidden.bs.modal', function () {
                $('#schedule_active_toggle').prop('checked', savedState === '1');

                $('#autoSchedule input[type="text"]').each(function () {
                    var id = $(this).attr('id');
                    if (originalValues[id] !== undefined) {
                        $(this).val(originalValues[id]);
                    }
                });
            });


        });
    </script>
    <script>
        $(document).ready(function() {
            $(".auto, .manual").click(function(e) {
                e.preventDefault();
                e.stopImmediatePropagation();
                $.ajax({
                    url: "{{ url('check-is-payable') }}",
                    type: "GET",
                    success: function(response) {
                        if (response.is_payable === 0) {
                            Swal.fire({
                                title: @json(translate('dashboard_ewallet.warning')),
                                text: @json(translate('dashboard_ewallet.setup_payout_method_first')),
                                icon: "warning",
                                confirmButtonText: @json(translate('dashboard_ewallet.ok')),
                                showCancelButton: false
                            });
                        } else {
                            if ($(e.target).hasClass('manual')) {
                                $('#request_wd').modal('show');
                            } else if ($(e.target).hasClass('auto')) {
                                $('#autoSchedule').modal('show');
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(error);
                        Swal.fire({
                            title: @json(translate('dashboard_ewallet.error')),
                            text: @json(translate('dashboard_ewallet.unable_to_verify_payout_status')),
                            icon: "error",
                            confirmButtonText: "OK"
                        });
                    }
                });
            });
        });
    </script>

    <script>
        $(document).on('click', '.filter-option', function() {
            var filter = $(this).data('filter');
            var filterText = $(this).text();
            $('#selected-filter').html(filterText + ' <span><i class="fa-solid fa-angle-down ms-1"></i></span>');
            $.ajax({
                url: "{{ route('get-earning-data') }}",
                method: "GET",
                data: {
                    filter: filter
                },
                success: function(response) {
                    updateCharts(response);
                }
            });
        });

        // function updateCharts(data) {
        //     var canvas = document.getElementById('earningChart');
        //     new Chart(canvas, {
        //         type: 'pie',
        //         data: {
        //             labels: [
        //                 @json(translate('dashboard_ewallet.total_earning')),
        //                 @json(translate('dashboard_ewallet.total_cancellation')),
        //                 @json(translate('dashboard_ewallet.adjusments')),
        //                 @json(translate('dashboard_ewallet.fees_paid'))
        //             ],
        //             datasets: [{
        //                 data: [data.total_earning, data.total_cancellation, data.adjustments, data.platform_fee],
        //                 backgroundColor: ['#A2DE5B', '#e65862', '#2f2e2e5a', '#D16DFF']
        //             }]
        //         },
        //         options: {
        //             responsive: true,
        //             maintainAspectRatio: true,
        //             legend: {
        //                 display: true,
        //                 position: 'top',
        //                 align: 'center',
        //                 labels: {
        //                     usePointStyle: true,
        //                     padding: 20,
        //                     generateLabels: function(chart) {
        //                         const data = chart.data;
        //                         if (data.labels.length && data.datasets.length) {
        //                             return data.labels.map(function(label, i) {
        //                                 const meta = chart.getDatasetMeta(0);
        //                                 const style = meta.controller.getStyle(i);
        //                                 return {
        //                                     text: label,
        //                                     fillStyle: style.backgroundColor,
        //                                     strokeStyle: style.borderColor,
        //                                     lineWidth: style.borderWidth,
        //                                     pointStyle: 'circle',
        //                                     hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
        //                                     index: i
        //                                 };
        //                             });
        //                         }
        //                         return [];
        //                     }
        //                 }
        //             },
        //             tooltips: {
        //                 callbacks: {
        //                     label: function(tooltipItem, data) {
        //                         const label = data.labels[tooltipItem.index] || '';
        //                         const rawValue = data.datasets[tooltipItem.datasetIndex].data[tooltipItem
        //                             .index];
        //                         const numValue = Number(rawValue); // Ensure it's a number
        //                         const formattedValue = numValue.toLocaleString('en-US', {
        //                             minimumFractionDigits: 2,
        //                             maximumFractionDigits: 2
        //                         });
        //                         return `${label}: ${formattedValue}`;
        //                     }
        //                 }
        //             },
        //             plugins: {
        //                 labels: {
        //                     render: 'percentage',
        //                     fontColor: ['#fff', '#fff', '#fff', '#fff'],
        //                     precision: 2
        //                 }
        //             },
        //         }
        //     });
        //     var canvas1 = document.getElementById('earningChart1');
        //     new Chart(canvas1, {
        //         type: 'pie',
        //         data: {
        //             labels: [
        //                 @json(translate('dashboard_ewallet.outstanding_payments')),
        //                 @json(translate('dashboard_ewallet.incoming_withdrawal')),
        //                 @json(translate('dashboard_ewallet.in_transit'))
        //             ],
        //             pointStyle: 'circle',

        //             datasets: [{
        //                 data: [data.outstanding_payments, data.pending_withdrawl, data.in_process],
        //                 backgroundColor: ['#446CB0', '#ff7932', '#ffce32']
        //             }]
        //         },
        //         options: {
        //             responsive: true,
        //             maintainAspectRatio: true,
        //             legend: {
        //                 display: true,
        //                 position: 'top',
        //                 align: 'center',
        //                 labels: {
        //                     usePointStyle: true,
        //                     padding: 20,
        //                     generateLabels: function(chart) {
        //                         const data = chart.data;
        //                         if (data.labels.length && data.datasets.length) {
        //                             return data.labels.map(function(label, i) {
        //                                 const meta = chart.getDatasetMeta(0);
        //                                 const style = meta.controller.getStyle(i);
        //                                 return {
        //                                     text: label,
        //                                     fillStyle: style.backgroundColor,
        //                                     strokeStyle: style.borderColor,
        //                                     lineWidth: style.borderWidth,
        //                                     pointStyle: 'circle',
        //                                     hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
        //                                     index: i
        //                                 };
        //                             });
        //                         }
        //                         return [];
        //                     }
        //                 }
        //             },
        //             tooltips: {
        //                 callbacks: {
        //                     label: function(tooltipItem, data) {
        //                         const label = data.labels[tooltipItem.index] || '';
        //                         const rawValue = data.datasets[tooltipItem.datasetIndex].data[tooltipItem
        //                             .index];
        //                         const numValue = Number(rawValue); // Ensure it's a number
        //                         const formattedValue = numValue.toLocaleString('en-US', {
        //                             minimumFractionDigits: 2,
        //                             maximumFractionDigits: 2
        //                         });
        //                         return `${label}: ${formattedValue}`;
        //                     }
        //                 }
        //             },
        //             plugins: {
        //                 labels: {
        //                     render: 'percentage',
        //                     fontColor: ['#fff', '#fff', '#fff'],
        //                     precision: 2
        //                 }
        //             },
        //         }
        //     });
        // }


        var earningChart = null;
        var earningChart1 = null;

        function initSecondChart(data) {
            var canvas1 = document.getElementById('earningChart1');
            earningChart1 = new Chart(canvas1, {
                type: 'pie',
                data: {
                    labels: [
                        @json(translate('dashboard_ewallet.outstanding_payments')),
                        @json(translate('dashboard_ewallet.incoming_withdrawal')),
                        @json(translate('dashboard_ewallet.in_transit'))
                    ],
                    datasets: [{
                        data: [data.outstanding_payments, data.pending_withdrawl, data.in_process],
                        backgroundColor: ['#446CB0', '#ff7932', '#ffce32']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'center',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map(function(label, i) {
                                        const meta = chart.getDatasetMeta(0);
                                        const style = meta.controller.getStyle(i);
                                        return {
                                            text: label,
                                            fillStyle: style.backgroundColor,
                                            strokeStyle: style.borderColor,
                                            lineWidth: style.borderWidth,
                                            pointStyle: 'circle',
                                            hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltips: {
                        callbacks: {
                            label: function(tooltipItem, data) {
                                const label = data.labels[tooltipItem.index] || '';
                                const rawValue = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
                                const numValue = Number(rawValue); 
                                const formattedValue = numValue.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                                return `${label}: ${formattedValue}`;
                            }
                        }
                    },
                    plugins: {
                        labels: {
                            render: 'percentage',
                            fontColor: ['#fff', '#fff', '#fff'],
                            precision: 2
                        }
                    },
                }
            });
        }

        function updateCharts(data) {
            if (earningChart) {
                earningChart.destroy();
            }
            // if (earningChart1) {
            //     earningChart1.destroy();
            // }

            var canvas = document.getElementById('earningChart');
            earningChart = new Chart(canvas, {
                type: 'pie',
                data: {
                    labels: [
                        @json(translate('dashboard_ewallet.total_earning')),
                        @json(translate('dashboard_ewallet.total_cancellation')),
                        @json(translate('dashboard_ewallet.adjusments')),
                        @json(translate('dashboard_ewallet.fees_paid'))
                    ],
                    datasets: [{
                        data: [data.total_earning, data.total_cancellation, data.adjustments, data.platform_fee],
                        backgroundColor: ['#A2DE5B', '#e65862', '#2f2e2e5a', '#D16DFF']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'center',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            generateLabels: function(chart) {
                                const data = chart.data;
                                if (data.labels.length && data.datasets.length) {
                                    return data.labels.map(function(label, i) {
                                        const meta = chart.getDatasetMeta(0);
                                        const style = meta.controller.getStyle(i);
                                        return {
                                            text: label,
                                            fillStyle: style.backgroundColor,
                                            strokeStyle: style.borderColor,
                                            lineWidth: style.borderWidth,
                                            pointStyle: 'circle',
                                            hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
                                            index: i
                                        };
                                    });
                                }
                                return [];
                            }
                        }
                    },
                    tooltips: {
                        callbacks: {
                            label: function(tooltipItem, data) {
                                const label = data.labels[tooltipItem.index] || '';
                                const rawValue = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
                                const numValue = Number(rawValue); 
                                const formattedValue = numValue.toLocaleString('en-US', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                                return `${label}: ${formattedValue}`;
                            }
                        }
                    },
                    plugins: {
                        labels: {
                            render: 'percentage',
                            fontColor: ['#fff', '#fff', '#fff', '#fff'],
                            precision: 2
                        }
                    },
                }
            });

            // var canvas1 = document.getElementById('earningChart1');
            // earningChart1 = new Chart(canvas1, {
            //     type: 'pie',
            //     data: {
            //         labels: [
            //             @json(translate('dashboard_ewallet.outstanding_payments')),
            //             @json(translate('dashboard_ewallet.incoming_withdrawal')),
            //             @json(translate('dashboard_ewallet.in_transit'))
            //         ],
            //         pointStyle: 'circle',

            //         datasets: [{
            //             data: [data.outstanding_payments, data.pending_withdrawl, data.in_process],
            //             backgroundColor: ['#446CB0', '#ff7932', '#ffce32']
            //         }]
            //     },
            //     options: {
            //         responsive: true,
            //         maintainAspectRatio: true,
            //         legend: {
            //             display: true,
            //             position: 'top',
            //             align: 'center',
            //             labels: {
            //                 usePointStyle: true,
            //                 padding: 20,
            //                 generateLabels: function(chart) {
            //                     const data = chart.data;
            //                     if (data.labels.length && data.datasets.length) {
            //                         return data.labels.map(function(label, i) {
            //                             const meta = chart.getDatasetMeta(0);
            //                             const style = meta.controller.getStyle(i);
            //                             return {
            //                                 text: label,
            //                                 fillStyle: style.backgroundColor,
            //                                 strokeStyle: style.borderColor,
            //                                 lineWidth: style.borderWidth,
            //                                 pointStyle: 'circle',
            //                                 hidden: isNaN(data.datasets[0].data[i]) || meta.data[i].hidden,
            //                                 index: i
            //                             };
            //                         });
            //                     }
            //                     return [];
            //                 }
            //             }
            //         },
            //         tooltips: {
            //             callbacks: {
            //                 label: function(tooltipItem, data) {
            //                     const label = data.labels[tooltipItem.index] || '';
            //                     const rawValue = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];
            //                     const numValue = Number(rawValue); 
            //                     const formattedValue = numValue.toLocaleString('en-US', {
            //                         minimumFractionDigits: 2,
            //                         maximumFractionDigits: 2
            //                     });
            //                     return `${label}: ${formattedValue}`;
            //                 }
            //             }
            //         },
            //         plugins: {
            //             labels: {
            //                 render: 'percentage',
            //                 fontColor: ['#fff', '#fff', '#fff'],
            //                 precision: 2
            //             }
            //         },
            //     }
            // });
        }



        $(document).ready(function() {
            var defaultFilter = 'overall'; // Default to 'overall'
            $.ajax({
                url: "{{ route('get-earning-data') }}",
                method: "GET",
                data: {
                    filter: defaultFilter
                },
                success: function(response) {
                    updateCharts(response);
                    initSecondChart(response);
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $('.cop').on('input', function() {
                let value = $(this).val();
                value = value.replace(/[^\d]/g, '');
                formatValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                $(this).val(formatValue);
            });
            // Format the initial value when the modal opens
            $('#autoSchedule').on('shown.bs.modal', function() {
                let scheduleAmount = $('#schedule_amount');
                let value = scheduleAmount.val();
                if (value) {
                    value = value.replace(/,/g, '');
                    formatValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    scheduleAmount.val(formatValue);
                }
            });
            $("#schedule_active_toggle").change(function() {
                let status = $(this).is(":checked") ? 1 : 0;
                $("#schedule_active_hidden").val(status);
            });
            $('#signup-btn').click(function() {
                // Remove commas before submitting to ensure proper numeric value
                let scheduleAmount = $('#schedule_amount');
                let rawValue = scheduleAmount.val().replace(/,/g, '');
                // Create a temporary hidden input with the raw value
                $('<input>').attr({
                    type: 'hidden',
                    name: 'raw_schedule_amount',
                    value: rawValue
                }).appendTo('#signUp-form');
                // Continue with form submission
                // Your existing form submission code here
            });
        });
    </script>
@endpush
