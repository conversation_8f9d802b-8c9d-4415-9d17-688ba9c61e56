@php
    $step_data = $category?->cms_steppers->firstWhere('step_name', 'engine-type');
@endphp
<fieldset class="select_categories_step min_stay_requirement_step">
    <div class="inner_section_fieldset h_100">
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_col">
                    <div class="main_step_title">
                        <h2>{{ $step_data->title }}</h2>
                        @isset($step_data->sub_title)
                            <p>{{ $step_data->sub_title }}</p>
                        @endisset
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="inner_section_categories_main_col inner_section_engine_main_col scrollable-section">
                    <div class="row">
                        @php
                            // $engine_types = [
                            //     'diesel_engine.png' => translate('stepper.diesel') ,
                            //     'electric_engine.png' => translate('stepper.electric'),
                            //     'gasoline_engine.png' => translate('stepper.gasoline'),
                            //     'hydrogen_engine.png' => translate('stepper.hydrogen'),
                            //     'hybrid_engine.png' => translate('stepper.hybrid'),
                            //     'no_engine.png' => translate('stepper.no_engine'),
                            //     'other_engine.png' => translate('stepper.other'),
                            // ];
                            $engine_types = [
                                'diesel' => [
                                    'icon' => 'diesel_engine.png',
                                    'label' => translate('stepper.diesel'),
                                ],
                                'electric' => [
                                    'icon' => 'electric_engine.png',
                                    'label' => translate('stepper.electric'),
                                ],
                                'gasoline' => [
                                    'icon' => 'gasoline_engine.png',
                                    'label' => translate('stepper.gasoline'),
                                ],
                                'hydrogen' => [
                                    'icon' => 'hydrogen_engine.png',
                                    'label' => translate('stepper.hydrogen'),
                                ],
                                'hybrid' => [
                                    'icon' => 'hybrid_engine.png',
                                    'label' => translate('stepper.hybrid'),
                                ],
                                'no_engine' => [
                                    'icon' => 'no_engine.png',
                                    'label' => translate('stepper.no_engine'),
                                ],
                                'other' => [
                                    'icon' => 'other_engine.png',
                                    'label' => translate('stepper.other'),
                                ],
                            ];
                        @endphp
                        @foreach ($engine_types as $engine_type => $attributes)
                            <div class="col-12 col-sm-6 col-md-4 col-lg-3 col-xl-3 single_engine_col">
                                <div class="inner_section_single_category">
                                    <input type="radio" name="engine_type" value="{{ $engine_type }}"
                                        @if (isset($listing) && $listing->detail->engine_type == $engine_type) checked @endif
                                        id="engine_type_{{ $loop->index }}">
                                    <label for="engine_type_{{ $loop->index }}">
                                        <div class="category_icon_wrapper">
                                            <img src="{{ asset('website/images/' . $attributes['icon']) }}"
                                                alt="{{ $attributes['label'] }}">
                                        </div>
                                        <div class="category_title">
                                            <h5>{{ $attributes['label'] }}</h5>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        @endforeach

                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="button" name="next" class="next action-button btn button1" value="{{ translate('stepper.next') }}"
        disabled />
    <input type="button" name="previous" class="previous action-button-previous"
        value="{{ translate('stepper.back') }}" />
</fieldset>

@push('js')
    <script>
        $(document).ready(function() {

            function listingEngineValidation() {
                var checkedEngineCount = 0;
                var test = 0;
                $('.select_categories_step .inner_section_engine_main_col .single_engine_col').each(function() {
                    if ($(this).find('input[type="radio"]').is(':checked')) {
                        checkedEngineCount++;
                    }
                });
                if (checkedEngineCount == 0) {
                    $('.select_categories_step:has(.inner_section_engine_main_col) .next').prop('disabled', true);
                    console.log('Not Checked Engine');
                } else {
                    $('.select_categories_step:has(.inner_section_engine_main_col) .next').prop('disabled', false);
                }
            }

            listingEngineValidation();

            $(document).on('change',
                '.select_categories_step .inner_section_engine_main_col .single_engine_col input[type="radio"]',
                function() {
                    listingEngineValidation();
                });
        });
    </script>
@endpush
