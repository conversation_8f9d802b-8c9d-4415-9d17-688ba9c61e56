<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <meta name="description" content="{{ App\CommonSetting::first()->description ?? '' }}">
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <link rel="icon" type="image/png" sizes="16x16"
        href="{{ asset('') }}{{ App\CommonSetting::first()->favicon ?? '' }}">
    <title>{{ App\CommonSetting::first()->title ?? '' }}</title>
    <!-- ===== Bootstrap CSS ===== -->
    <link href="{{ asset('bootstrap/dist/css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- ===== Plugin CSS ===== -->
    @if (!Request::is('listing/*'))
        <link href="{{ asset('plugins/components/chartist-js/dist/chartist.min.css') }}" rel="stylesheet">
        <link href="{{ asset('plugins/components/chartist-plugin-tooltip-master/dist/chartist-plugin-tooltip.css') }}"
            rel="stylesheet">
    @endif
    <link href="{{ asset('plugins/components/toast-master/css/jquery.toast.css') }}" rel="stylesheet">

    <!-- ===== Animation CSS ===== -->
    <link href="{{ asset('css/animate.css') }}" rel="stylesheet">
    <!-- ===== Custom CSS ===== -->
    <link href="{{ asset('css/common.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    {{-- include translation --}}
    @stack('css')

    <!--====== Dynamic theme changing =====-->

    @if (session()->get('theme-layout') == 'fix-header')
        <link href="{{ asset('css/style-fix-header.css') }}" rel="stylesheet">
        <link href="{{ asset('css/colors/default.css') }}" id="theme" rel="stylesheet">
    @elseif(session()->get('theme-layout') == 'mini-sidebar')
        <link href="{{ asset('css/style-mini-sidebar.css') }}" rel="stylesheet">
        <link href="{{ asset('css/colors/default.css') }}" id="theme" rel="stylesheet">
    @else
        <link href="{{ asset('css/style-normal.css') }}" rel="stylesheet">
        <link href="{{ asset('css/dashboard.css') }}" rel="stylesheet">
        <link href="{{ asset('css/dashboard-responsive.css') }}" rel="stylesheet">
        <link href="{{ asset('css/colors/default.css') }}" id="theme" rel="stylesheet">
    @endif
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-iconpicker/1.9.0/css/bootstrap-iconpicker.min.css" />
    <style>
        @media (min-width: 768px) {
            .extra.collapse li a span.hide-menu {
                display: block !important;
            }

            .extra.collapse.in li a.waves-effect span.hide-menu {
                display: block !important;
            }

            .extra.collapse li.active a.active span.hide-menu {
                display: block !important;
            }

            ul.side-menu li:hover+.extra.collapse.in li.active a.active span.hide-menu {
                display: block !important;
            }
        }

        /* Custom toast styles */
        .toast {
            min-width: 300px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background-color: white;
            border: 1px solid rgba(0, 0, 0, .125);
            border-radius: 0.25rem;
            margin-bottom: 0.5rem;
            opacity: 1;
            transition: opacity 0.3s ease-in-out;
        }

        .toast.hide {
            opacity: 0;
        }

        .toast-header {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            color: #6c757d;
            background-color: rgba(0, 0, 0, .03);
            border-bottom: 1px solid rgba(0, 0, 0, .125);
            border-top-left-radius: calc(0.25rem - 1px);
            border-top-right-radius: calc(0.25rem - 1px);
        }

        .toast-header img {
            border: 1px solid #dee2e6;
            margin-right: 0.5rem;
        }

        .toast-header .close {
            margin-left: auto;
            margin-bottom: 0;
            padding: 0;
            background: transparent;
            border: 0;
            font-size: 1.5rem;
            line-height: 1;
            color: #000;
            opacity: 0.5;
            cursor: pointer;
        }

        .toast-header .close:hover {
            opacity: 0.75;
        }

        .toast-body {
            word-wrap: break-word;
            padding: 0.75rem;
        }

        .toast-container {
            position: fixed !important;
            top: 1rem !important;
            right: 1rem !important;
            z-index: 9999 !important;
        }

        /* Message badge styles for dashboard navbar */
        .navbar-top-links .position-reltive .badge {
            background: #ff6849 !important;
            border-radius: 50% !important;
            color: white !important;
            font-size: 10px !important;
            height: 18px !important;
            width: 18px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            top: 7px !important;
            right: 16px !important;
        }

        .navbar-top-links .position-reltive .badge p {
            margin-top: 10px;
        }
    </style>
</head>

<body class="@if (session()->get('theme-layout')) {{ session()->get('theme-layout') }} @endif">
    <div id="sec-loader">
        <div class="load">
            <img src="{{ asset('website') }}/images/luxustar-loader.gif" alt="Loader">
        </div>
    </div>
    <!-- ===== Main-Wrapper ===== -->
    <div id="wrapper">
        <div class="preloader">
            <div class="cssload-speeding-wheel"></div>
        </div>
        <!-- ===== Top-Navigation ===== -->
        @include('layouts.partials.navbar')
        <!-- ===== Top-Navigation-End ===== -->

        <!-- ===== Left-Sidebar ===== -->
        @include('layouts.partials.sidebar')
        @include('layouts.partials.right-sidebar')

        <!-- ===== Left-Sidebar-End ===== -->
        <!-- ===== Page-Content ===== -->
        <div class="page-wrapper">
            @yield('content')
            <footer class="footer t-a-c">

                <div class="p-20 ">
                    <a> © {{ date('Y') }} {{ translate('footer.all_rights_reserved') }}</a>
                    <!-- {{ App\CommonSetting::first()->footer_text ?? '' }}  -->
                    <div class="footer_link">
                        <a href="{{ route('privacy_policy', ['locale' => app()->getLocale()]) }}">{{ translate('footer.privacy_policy') }}
                            |</a></li>
                        <a href="{{ route('terms', ['locale' => app()->getLocale()]) }}">{{ translate('footer.terms_conditions') }}
                            |</a></li>
                        <a href="{{ route('supplier_aggreement', ['locale' => app()->getLocale()]) }}">{{ translate('footer.supplier_agreement') }}
                        </a></li>
                    </div>
                </div>

            </footer>
        </div>
        <!-- ===== Page-Content-End ===== -->
    </div>
    <!-- ===== Main-Wrapper-End ===== -->
    <audio src="{{ asset('website/messenger/notification-audio.wav') }}" id="notification-audio"
        preload="auto"></audio>

    <!-- Toast Container for Message Notifications -->
    <div aria-live="polite" aria-atomic="true" class="position-relative">
        <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
            <!-- Dynamic toasts will be inserted here -->
        </div>
    </div>




    <!-- ==============================
    Required JS Files
=============================== -->
    <!-- ===== jQuery ===== -->
    <script src="{{ asset('plugins/components/jquery/dist/jquery.min.js') }}"></script>
    <!-- ===== Bootstrap JavaScript ===== -->
    <script src="{{ asset('bootstrap/dist/js/bootstrap.min.js') }}"></script>
    <!-- ===== Slimscroll JavaScript ===== -->
    <script src="{{ asset('js/jquery.slimscroll.js') }}"></script>
    <!-- ===== Wave Effects JavaScript ===== -->
    <script src="{{ asset('js/waves.js') }}"></script>
    <!-- ===== Menu Plugin JavaScript ===== -->
    <script src="{{ asset('js/sidebarmenu.js') }}"></script>
    <!-- ===== Custom JavaScript ===== -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    @if (session()->get('theme-layout') == 'fix-header')
        <script src="{{ asset('js/custom-fix-header.js') }}"></script>
    @elseif(session()->get('theme-layout') == 'mini-sidebar')
        <script src="{{ asset('js/custom-mini-sidebar.js') }}"></script>
    @else
        <script src="{{ asset('js/custom-normal.js') }}"></script>
    @endif
    @include('website.layout.translate')


    {{-- <script src="{{asset('js/custom.js')}}"></script> --}}
    <!-- ===== Plugin JS ===== -->
    @if (!Request::is('listing/*') && !Request::is('e-wallet'))
        <script src="{{ asset('plugins/components/chartist-js/dist/chartist.min.js') }}"></script>
        <script src="{{ asset('plugins/components/chartist-plugin-tooltip-master/dist/chartist-plugin-tooltip.min.js') }}">
        </script>
    @endif
    <script src="{{ asset('plugins/components/sparkline/jquery.sparkline.min.js') }}"></script>
    <script src="{{ asset('plugins/components/sparkline/jquery.charts-sparkline.js') }}"></script>
    <script src="{{ asset('plugins/components/knob/jquery.knob.js') }}"></script>
    <script src="{{ asset('plugins/components/easypiechart/dist/jquery.easypiechart.min.js') }}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key={{ google_map_key() }}&callback=initMap" async defer></script>
    <!-- ===== Style Switcher JS ===== -->
    <script src="{{ asset('plugins/components/styleswitcher/jQuery.style.switcher.js') }}"></script>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-iconpicker/1.9.0/js/bootstrap-iconpicker-iconset-all.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-iconpicker/1.9.0/js/bootstrap-iconpicker.min.js"></script>

    {{-- custom added scripts --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    {{-- custom added scripts --}}
    <script type="text/javascript">
        @if (session()->has('message'))
            Swal.fire({
                title: "{{ session()->get('title') ?? 'Success!' }}",
                html: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 5000,
                buttons: false,
            });
        @endif
        @if (session()->has('flash_message'))
            Swal.fire({
                title: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message'))) }}",
                {{--            html: "{{session()->get('flash_message')}}", --}}
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 5000,
                buttons: false,
            });
        @endif
    </script>

    @include('includes.pusher-notification-toast')
    @if (
        !Request::is('helpCenter/help-center/*') &&
            !Request::is('listing/*') &&
            !Request::is('e-wallet') &&
            !Request::is('cms'))
        <script src="{{ asset('website/assets_cdn/ckeditor/ckeditor.js') }}"></script>

        @php
            $ckeditor_links = [
                'privacy_policy_description',
                'term_condtion_description',
                'supplier_description',
                'about[0][description]',
                'about[1][description]',
                'about[2][description]',
            ];
        @endphp
        <script>
            @foreach ($ckeditor_links as $ckeditor_link)
                CKEDITOR.replace('{{ $ckeditor_link }}', {
                    allowedContent: true, // Allow all content
                    extraAllowedContent: 'p h1 h2 h3 h4 h5 h6 strong em; a[!href]; ul ol li; img[!src,alt,width,height]',
                    disallowedContent: 'script; *[on*]',
                    removePlugins: 'paste,sourcearea,image,scayt,templates,about,forms,table,tabletools,tableselection,iframe,div,language',
                    removeButtons: 'ExportPdf,NewPage,Save' // Remove Export to PDF button
                });
            @endforeach
        </script>
    @endif

    <script>
        $("#searchBar").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            var rowCount = 0;
            $("table tbody tr").filter(function() {
                var match = $(this).text().toLowerCase().indexOf(value) > -1;
                $(this).toggle(match);
                if (match) rowCount++;
            });

            if (rowCount === 0) {
                $("table tbody").append(
                    '<tr class="no_found_data"><td colspan="8" class="text-center no-data-found">No data found</td></tr>'
                );
            } else {
                $(".no-data-found").text("");
                $('.no_found_data').remove();
            }
        });

        $(document).on("click", ".delete-btn", function() {
            var item_id = $(this).data("id");
            Swal.fire({
                title: "Are you sure?",
                text: "This action can't be reverted!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Yes, delete it!"
            }).then((result) => {
                if (result.isConfirmed) {
                    $("#delete-form-" + item_id).submit();
                }
            });
        });
        $('#searchBar').on('keypress', function(event) {
            if (event.keyCode === 13) {
                event.preventDefault();
                return false;
            }
        });
    </script>
    <script>
        var loader = document.getElementById("sec-loader");
        window.addEventListener("load", function() {
            loader.style.display = "none"
        });
    </script>

    {{-- <script>

        $(document).ready(function () {
            function adjustFooter() {
                // var windowHeight = $(window).height();
                var windowHeight = $(window).height() - 80;
                var footerPositionOff = $('footer').offset();
                var footerPosition = footerPositionOff.top;
                var relativePoint = windowHeight - 110;

                console.log(windowHeight);
                console.log(footerPosition);
                console.log(relativePoint);

                if (footerPosition < relativePoint) {
                    $('footer').css({ 'position': 'absolute', 'left': '', 'margin-top': '' });
                    $('#wrapper .page-wrapper').css('padding-bottom', '30px');
                } else {
                    $('footer').css({ 'position': 'relative', 'left': 'unset', 'margin-top': '30px' });
                    $('#wrapper .page-wrapper').css('padding-bottom', '0px');
                }
            }

            // Run once on page load
            adjustFooter();

            // Watch for class changes on all children inside .page-wrapper
            var targetNode = document.querySelector('.page-wrapper');
            if (targetNode) {
                var observer = new MutationObserver(function (mutationsList) {
                    mutationsList.forEach(function (mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            console.log("Class changed on:", mutation.target); // 🟢 debug

                            // // Instantly kill animations + hide footer
                            // $('footer').stop(true, true).css('opacity', 0);

                            // Delay and then fade back in with footer adjustment
                            // setTimeout(() => {
                                adjustFooter();
                                // $('footer').stop(true, true).fadeTo(400, 1); // fade-in 400ms
                            // }, 1000);
                        }
                    });
                });

                observer.observe(targetNode, {
                    attributes: true,
                    attributeFilter: ['class'], // only watch class changes
                    subtree: true               // include all children
                });
            }

            // Also adjust on window resize
            $(window).on('resize', adjustFooter);
        });




    </script> --}}

    @stack('js')

</body>

</html>
