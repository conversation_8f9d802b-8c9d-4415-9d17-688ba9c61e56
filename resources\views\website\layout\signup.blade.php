@push('css')
    <style>
        /* Hide all fieldsets except the first one initially */
        #signUp fieldset {
            display: none;
        }
        #signUp fieldset:first-child {
            display: block;
        }
    </style>


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css">

    <style>
        #signUp .flag-icon {
            width: 20px;
            height: 15px;
            margin-right: 8px;
            display: inline-block;
            background-size: cover;
            background-position: center;
            border-radius: 2px;
        }

        #signUp .country-select-wrapper {
            position: relative;
        }

        #signUp #country-select-signup {
            padding-left: 40px;
            background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 12px;
            padding-right: 40px;
        }

        #signUp .selected-flag {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            z-index: 2;
        }

        #signUp .country-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
        }

        #signUp .country-option:hover {
            background-color: #f8f9fa;
        }


        #signUp .select2.select2-container {height: 50px; margin-bottom: 5px; border: 0; padding: 0;}
        #signUp .select2.select2-container .selection {height: 100%; display: block;}
        #signUp .select2.select2-container .select2-selection {height: 100%; display: flex; align-items: center;  border-radius: 15px; border: 1px solid #ced4da;}
        #signUp .select2.select2-container .select2-selection__arrow {height: 50px;}

        #signUp .select2.select2-container.select2-container--below.select2-container--open .select2-selection {border-bottom-left-radius: 0; border-bottom-right-radius: 0; border-bottom: 0;}
        #signUp .select2.select2-container.select2-container--above.select2-container--open .select2-selection {border-top-left-radius: 0; border-top-right-radius: 0; border-top: 0;}

        #signUp .country-select-wrapper .select2 .select2-selection {background-color: unset; border: 0; border-radius: 0;}
        #signUp .country-select-wrapper .select2 {border: 1px solid var(--light-grey); border-radius: 12px; padding: 6.5px 20px; text-align: left;}
        #signUp .country-select-wrapper .select2 .select2-selection__rendered {padding: 0; font-size: 14px;}
        #signUp .country-select-wrapper .select2 .select2-selection__arrow {top: 0; bottom: 0; margin: auto 0}

        /* .select2-container:has([id*="select2-country-select-signup"]) .select2-dropdown {border: 1px solid var(--light-grey);}
        body:has(.setting_page .select2.select2-container.select2-container--below.select2-container--open) .select2-dropdown {border-top: 0; box-shadow: none; border-top-left-radius: 0; border-top-right-radius: 0; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;}
        body:has(.setting_page .select2.select2-container.select2-container--above.select2-container--open) .select2-dropdown {border-bottom: 0; box-shadow: none; border-bottom-left-radius: 0; border-bottom-right-radius: 0; border-top-left-radius: 10px; border-top-right-radius: 10px;} */


    </style>


@endpush

<div class="modal fade login signup" id="signUp" data-bs-backdrop="static" tabindex="-1"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content p-4">
            <form method="POST" id="signUp-form">
                @csrf
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.sign_up_to') }} <span
                                class="color luxu">LuxuStars</span></h4>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center" style="padding-top: 0">
                        <p class="fs-14 bold red">{{ translate('home.id_match_warning') }}</p>

                        <div id="sign-up-error" style="display: none" class="alert alert-danger text-start "
                            role="alert">
                            <ul>
                            </ul>
                        </div>

                        <div class="row">
                            <div class="col-md-6" style="padding-right: 5px;">
                                <div class="form-outline mb-3">
                                    <input type="name" class="form-control" name="first_name"
                                        placeholder="{{ translate('home.first_name') }}" required />
                                </div>
                            </div>
                            <div class="col-md-6" style="padding-left: 5px;">
                                <div class="form-outline mb-3">
                                    <input type="name" class="form-control" name="last_name"
                                        placeholder="{{ translate('home.last_name') }}" required />
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-outline mb-3">
                                    <input type="email" class="form-control" id="user_email" name="email"
                                        placeholder="{{ translate('home.email_address') }}" required />
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-outline mb-3">
                                    <input type="tel" class="form-control" id="telephone" maxlength="12" name="phone"
                                        placeholder="{{ translate('home.phone') }}" pattern="[0-9]{3} [0-9]{3} [0-9]{4}"
                                        required />
                                    <input type="hidden" class="country_code" name="country_code" value="+1" id="country_code">
                                </div>
                            </div>
                            <div class="col-md-12">
                                {{-- <div class="col-md-12"> --}}
                                    <div class="form-outline mb-3">
                                        <div class="country-select-wrapper position-relative">
                                            <select class="form-control @error('country') is-invalid @enderror"
                                                id="country-select-signup" name="country">
                                                {{-- <option value="" disabled selected>
                                                    {{ translate('user_account_setting.select_nationality') }}
                                                </option> --}}
                                                <option></option>
                                                @foreach ($countries as $country)
                                                    <option value="{{ $country->sortname }}"
                                                        data-flag="{{ $country->flags }}"
                                                        {{ (auth()->user()->profile->country ?? '') == $country->sortname ? 'selected' : '' }}>
                                                        {{ $country->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @error('country')
                                            <p class="text-danger mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                {{-- </div> --}}
                            </div>
                            <div class="col-md-12">
                                <div class="form-outline mb-3">
                                    <input type="password" id="createPassword" class="form-control" name="password"
                                        placeholder="{{ translate('home.password') }}" required />
                                    <i class="fas fa-eye eye_icon" id="pass_btn1"></i>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-outline mb-3">
                                    <input type="password" id="confirmPassword" class="form-control"
                                        name="password_confirmation" placeholder="{{ translate('home.confirm_password') }}"
                                        required />
                                    <i class="fas fa-eye eye_icon" id="pass_btn2"></i>
                                </div>
                            </div>
                        </div>


                        <div class="form-check term-condition">
                            <input class="form-check-input" type="checkbox" value="" id="terms_condition"
                                required>
                            <label class="form-check-label" for="terms_condition">
                                {{ translate('home.by_selecting') }} <span
                                    class="bold">{{ translate('home.sign_up') }}</span>,
                                {{ translate('home.i_agree_to') }} LuxuStars <a
                                    href="{{ route('terms', ["locale" => app()->getLocale()]) }}">{{ translate('home.terms_conditions') }}</a>,
                                {{ translate('home.and_acknowledge') }}
                                <a href="{{ route('privacy_policy', ["locale" => app()->getLocale()]) }}">{{ translate('home.privacy_policy') }}</a>.
                            </label>
                        </div>
                        <!-- Error message placeholder, initially hidden -->
                        <div id="terms-error" style="display: none; color: red; margin-bottom:10px;">
                            {{ translate('home.accept_terms_error') }}
                        </div>
                    </div>

                    <button type="button" class="btn button login btn-block mb-4"
                        id="signup-btn">{{ translate('home.sign_up') }}</button>

                    <!-- Register buttons -->
                    <div class="text-center">
                        <p class="fs-14">{{ translate('home.already_a_member') }} <button type="button"
                                class="not_btn blue" data-bs-toggle="modal" data-bs-dismiss="modal"
                                data-bs-target="#login">
                                {{ translate('home.sign_in') }}
                            </button>
                        </p>
                    </div>
                </fieldset>
                <!-- Phone Verification Step -->
                {{-- <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.check_your_phone') }}</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">{{ translate('home.sent_verification_code') }} <span
                                class="fw-bold user-phone-span"></span></p>
                        <div class="d-flex">
                            <input type="text" class="form-control" id="otp_inpt_phone"
                                placeholder="{{ translate('home.enter_verification_code') }}">
                        </div>
                    </div>
                    <input type="button" name="next" id="verify_phone_otp"
                        class="action-button btn button login btn-block mb-4 disable-on-click"
                        value="{{ translate('home.verify') }}" />
                    <button type="button" class="transparent button mx-auto w-100 b-none"
                        id="resend-phone-otp-btn">{{ translate('home.resend_code') }}</button>
                </fieldset> --}}

                <!-- Email Verification Step -->
                <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.check_your_email') }}</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">{{ translate('home.sent_verification_code') }} <span
                                class="fw-bold user-email-span"></span></p>
                        <div class="d-flex">
                            <input type="text" class="form-control" id="otp_inpt_email"
                                placeholder="{{ translate('home.enter_verification_code') }}">
                        </div>
                    </div>
                    <input type="button" name="next" id="verify_email_otp"
                        class="action-button btn button login btn-block mb-4 disable-on-click"
                        value="{{ translate('home.verify') }}" />
                    <div class="d-flex justify-content-end align-items-center w-100">
                        <button type="button" class="transparent button b-none py-0"
                            id="resend-email-otp-btn">{{ translate('home.resend_code') }}</button>
                        <span id="email_otp_timer" class="text-muted"></span>
                    </div>
                </fieldset>

                {{-- <fieldset>
                    <div class="modal-header b-none">
                        <h4 class="modal-title mx-auto">{{ translate('home.hoorah') }}</h4>
                    </div>
                    <div class="modal-body text-center">
                        <p class="fs-14">{{ translate('home.email_verified_success') }}</p>
                        <button type="button" class="btn btn-warning" onclick="location.reload()"
                            data-bs-dismiss="modal">{{ translate('home.close') }}</button>
                    </div>
                </fieldset> --}}
            </form>
        </div>
    </div>
</div>
</div>


@push('js')
    <script src="{{ asset('website') }}/js/jquery.toast.js"></script>
    <script>
        $(document).ready(function() {
            // Ensure only first fieldset is visible on load
            $('#signUp fieldset').hide();
            $('#signUp fieldset:first').show();
            console.log('Modal initialized - fieldsets count:', $('#signUp fieldset').length);
        });

        // Move functions outside of the click handler so they're globally accessible
        function phone_verify_submit(phone, country_code) {
            console.log('Phone verification started for:', phone);
            let otp = $("#otp_inpt_phone").val();
            let verify_btn = $("#verify_phone_otp");
            let email = $("#user_email").val(); // Get email for backend compatibility

            // Validate OTP input before sending request
            if (!otp || otp.trim() === '') {
                $.toast({
                    heading: 'Error',
                    text: 'Please enter the verification code',
                    showHideTransition: 'plain',
                    icon: 'error'
                });
                return;
            }

            verify_btn.html(`<div class="spinner-border" role="status"><span class="visually-hidden">{{ translate('home.loading') }}</span></div>`).prop('disabled', true);

            $.ajax({
                url: "{{ route('signUpMessageVerification') }}",
                type: "POST",
                data: {
                    email: email, // Send email as backend expects it
                    phone: phone,
                    country_code: country_code,
                    otp: otp,
                    type: "phone"
                },
                success: function(response) {
                    console.log('Phone verification response:', response);
                    if (response.status == true) {
                        // Phone verification successful, move to email verification
                        let current_fs = verify_btn.closest('fieldset');
                        let next_fs = current_fs.next('fieldset');

                        console.log('Moving from phone to email verification');
                        console.log('Current fieldset:', current_fs.length);
                        console.log('Next fieldset:', next_fs.length);

                        //show the next fieldset (email verification)
                        next_fs.show();
                        //hide the current fieldset with style
                        current_fs.animate({
                            opacity: 0
                        }, {
                            step: function(now) {
                                opacity = 1 - now;
                                current_fs.css({
                                    'display': 'none',
                                    'position': 'relative'
                                });
                                next_fs.css({
                                    'opacity': opacity
                                });
                            },
                            duration: 500
                        });
                    } else {
                        // Show the exact same error message as email verification
                        // $.toast({
                        //     heading: 'Error',
                        //     text: response.message || 'Invalid OTP or OTP expired',
                        //     showHideTransition: 'plain',
                        //     icon: 'error'
                        // })

                        Swal.fire({
                            title: 'Error',
                            text: response.message || 'Invalid OTP or OTP expired',
                            icon: 'error',
                            timer: 2000,
                            showConfirmButton: false
                        });

                    }
                },
                error: function(xhr, status, error) {
                    console.log('Phone verification error:', xhr.responseJSON);
                    let errorMessage = 'Invalid OTP or OTP expired';

                    // Try to get the exact error message from server response
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            let response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // Keep default message if parsing fails
                        }
                    }

                    // $.toast({
                    //     heading: 'Error',
                    //     text: errorMessage,
                    //     showHideTransition: 'plain',
                    //     icon: 'error'
                    // });

                    Swal.fire({
                        title: 'Error',
                        text: errorMessage,
                        icon: 'error',
                        timer: 2000,
                        showConfirmButton: false
                    });

                },
                complete: function(response) {
                    verify_btn.html("{{ translate('home.verify') }}").prop('disabled', false);
                }
            });
        }

        function email_verify_submit(email) {
            let otp = $("#otp_inpt_email").val();
            let verify_btn = $("#verify_email_otp");

            // Validate OTP input before sending request
            if (!otp || otp.trim() === '') {
                $.toast({
                    heading: 'Error',
                    text: 'Please enter the verification code',
                    showHideTransition: 'plain',
                    icon: 'error'
                });
                return;
            }

            verify_btn.html(`<div class="spinner-border" role="status"><span class="visually-hidden">{{ translate('home.loading') }}</span></div>`).prop('disabled', true);

            $.ajax({
                url: "{{ route('signUpEmailVerification') }}",
                type: "POST",
                data: {
                    email: email,
                    otp: otp,
                    type: "email"
                },
                success: function(response) {
                    if (response.status == true) {
                        Swal.fire({
                            title: "{{ translate('home.hoorah') }}",
                            text: "{{ translate('home.email_verified_success') }}",
                            icon: "success"
                        }).then((result) => {
                            if (result.isConfirmed) {
                                window.location.reload();
                            }
                        });
                    } else {

                        // $.toast({
                        //     heading: 'Error',
                        //     text: response.message || 'Invalid OTP or OTP expired',
                        //     showHideTransition: 'plain',
                        //     icon: 'error'
                        // })

                        Swal.fire({
                            title: 'Error',
                            text: response.message || 'Invalid OTP or OTP expired',
                            icon: 'error',
                            timer: 2000,
                            showConfirmButton: false
                        });

                    }
                },
                error: function(xhr, status, error) {
                    console.log('Email verification error:', xhr.responseJSON);
                    let errorMessage = 'Invalid OTP or OTP expired';

                    // Try to get the exact error message from server response
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        try {
                            let response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                errorMessage = response.message;
                            }
                        } catch (e) {
                            // Keep default message if parsing fails
                        }
                    }

                    // $.toast({
                    //     heading: 'Error',
                    //     text: errorMessage,
                    //     showHideTransition: 'plain',
                    //     icon: 'error'
                    // });

                    Swal.fire({
                        title: 'Error',
                        text: errorMessage,
                        icon: 'error',
                        timer: 2000,
                        showConfirmButton: false
                    });

                },
                complete: function(response) {
                    verify_btn.html("{{ translate('home.verify') }}").prop('disabled', false);
                }
            });
        }

        $("#signup-btn").on("click", function(e) {
            e.preventDefault();
            // Hide the error message when you try again
            $('#terms-error').hide();

            // Check if the terms and conditions checkbox is checked
            if (!$('#terms_condition').is(':checked')) {
                // If not checked, display the error message
                $('#terms-error').show();
                return; // Stop the function here
            }
            let sign_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('home.loading') }}</span></div>`
            ).prop('disabled', true);
            let signup_data = $("#signUp-form").serialize();
            let user_email = $("#user_email").val()
            let telephone = $("#telephone").val()
            let country_code = $("#country_code").val()
            $.ajax({
                url: "{{ route('sign_up') }}",
                data: signup_data,
                type: "POST",
                success: function(response) {
                    if (response.status == true) {
                        $("#sign-up-error").slideUp();
                        $("#signup-btn").addClass("next");
                        $(".user-email-span").html(user_email);
                        //$(".user-phone-span").html(telephone);

                        // Move to phone verification step first
                        setTimeout(() => {
                            console.log('Moving to phone verification step');
                            sign_btn.html("<b>{{ translate('home.next') }}</b>").removeAttr("id");
                            let current_fs = sign_btn.closest('fieldset');
                            let next_fs = current_fs.next('fieldset'); // This goes to phone verification

                            console.log('Current fieldset (signup):', current_fs.length);
                            console.log('Next fieldset (phone):', next_fs.length);

                            //show the next fieldset
                            next_fs.show();
                            //hide the current fieldset with style
                            current_fs.animate({
                                opacity: 0
                            }, {
                                step: function(now) {
                                    // for making fielset appear animation
                                    opacity = 1 - now;
                                    current_fs.css({
                                        'display': 'none',
                                        'position': 'relative'
                                    });
                                    next_fs.css({
                                        'opacity': opacity
                                    });
                                },
                                duration: 500
                            });
                        }, 50);
                    } else {

                        $('input').removeClass('is-invalid');
                        $("#sign-up-error").slideDown();
                        $("#sign-up-error ul").empty();

                        $.each(response.message, function(key, messages) {
                            // Loop through each message for this field
                            $.each(messages, function(_, message) {
                                $("#sign-up-error ul").append(`<li>${message}</li>`);
                            });
                            // Mark the input as invalid
                            $('input[name="' + key + '"]').addClass('is-invalid');
                        });

                        setTimeout(() => {
                            $("#sign-up-error").slideUp();
                        }, 10000);
                    }
                },
                complete: function() {
                    sign_btn.html("{{ translate('home.sign_up') }}").prop('disabled', false);
                }
            })



        });

        // Event handlers outside of signup button click - globally accessible
        $(document).on("click", "#verify_phone_otp", function() {
            let phone = $("#telephone").val();
            let country_code = $("#country_code").val();
            if (phone != "") {
                phone_verify_submit(phone,country_code);
            }
        });

        $(document).on("click", "#verify_email_otp", function() {
            let email = $("#user_email").val();
            if (email != "") {
                email_verify_submit(email);
            }
        });

        // resend phone otp
        $(document).on("click", "#resend-phone-otp-btn", function() {
            let phone = $("#telephone").val();
            let country_code = $("#country_code").val();
            let resend_otp_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('home.loading') }}</span></div>`
            ).prop('disabled', true);
            if (phone) {
                $.ajax({
                    url: "{{ route('resend_otp') }}",
                    type: "POST",
                    data: {
                        phone: phone,
                        country_code: country_code,
                        type: "phone",
                    },
                    success: function(response) {
                        resend_otp_btn.html("{{ translate('home.resend_code') }}")
                        if (response.status == true) {

                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'success'
                            // })

                            Swal.fire({
                                title: 'Success',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });

                        } else {

                            // $.toast({
                            //     heading: 'Error',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'error'
                            // })

                            Swal.fire({
                                title: 'Error',
                                text: response.message,
                                icon: 'error',
                                timer: 2000,
                                showConfirmButton: false
                            });

                        }
                    },
                    complete: function() {
                        setTimeout(() => {
                            resend_otp_btn.prop('disabled', false);
                        }, 1000 * 60);
                    }
                });
            }
        });

        // resend email otp
        $(document).on("click", "#resend-email-otp-btn", function() {
            let email = $("#user_email").val();
            let resend_otp_btn = $(this).html(
                `<div class="spinner-border" role="status"> <span class="visually-hidden">{{ translate('home.loading') }}</span></div>`
            ).prop('disabled', true);
            if (email) {
                $.ajax({
                    url: "{{ route('resend_otp') }}",
                    type: "POST",
                    data: {
                        email: email,
                        type: "email",
                    },
                    success: function(response) {
                        resend_otp_btn.html("{{ translate('home.resend_code') }}")
                        if (response.status == true) {
                            // Start the timer after successful resend
                            startEmailOtpTimer();
                            
                            // $.toast({
                            //     heading: 'Success',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'success'
                            // })

                            Swal.fire({
                                title: 'Success',
                                text: response.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            });


                        } else {
                            // Enable the button if there's an error
                            resend_otp_btn.prop('disabled', false);
                            
                            // $.toast({
                            //     heading: 'Error',
                            //     text: response.message,
                            //     showHideTransition: 'plain',
                            //     icon: 'error'
                            // })


                            Swal.fire({
                                title: 'Error',
                                text: response.message,
                                icon: 'error',
                                timer: 2000,
                                showConfirmButton: false
                            });

                        }
                    },
                    complete: function() {
                        // Remove the setTimeout since we're using the timer function
                    }
                });
            }
        });

        // Email OTP Timer Function
        let emailOtpTimerId = null;
        
        function startEmailOtpTimer() {
            let timeLeft = 60; // 1 minute in seconds
            const $timer = $("#email_otp_timer");
            const $resendBtn = $("#resend-email-otp-btn");
            
            // Clear any existing timer
            if (emailOtpTimerId) {
                clearInterval(emailOtpTimerId);
            }
            
            $resendBtn.prop("disabled", true);
            $resendBtn.hide();
            
            emailOtpTimerId = setInterval(function() {
                if (timeLeft <= 0) {
                    clearInterval(emailOtpTimerId);
                    emailOtpTimerId = null;
                    $timer.text("");
                    $resendBtn.prop("disabled", false);
                    $resendBtn.show();
                } else {
                    $timer.text(`Resend in ${timeLeft}s`);
                    timeLeft--;
                }
            }, 1000);
        }
        
        // Start the timer when email verification step is shown
        $(document).ready(function() {
            // Existing code...
            
            // When the signup form is submitted and email verification step is shown
            $("#signup-btn").on("click", function() {
                // Your existing code that shows the email verification step
                // ...
                
                // Start the timer when the email verification step is shown
                startEmailOtpTimer();
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $('#telephone').each(function() {
                const $input = $(this);

                $input.on('input', function() {
                    this.value = this.value.replace(/[^0-9\s\-]/g, '');
                });

                $input.on('keypress', function(event) {
                    if (event.which === 8 || event.which === 9 || event.which === 27 || event
                        .which === 13 ||
                        event.which === 46 || event.which === 45 || event.which === 40 || event
                        .which === 41 ||
                        event.which === 43 || event.which === 32) {
                        return true;
                    }
                    if (event.which >= 48 && event.which <= 57) {
                        return true;
                    }
                    event.preventDefault();
                });
                $input.removeAttr('min');
            });






            // $('#country-select-signup').select2({
            //     placeholder: 'Select a country',
            //     allowClear: true,
            //     width: '100%',
            //     templateResult: formatCountry,
            //     templateSelection: formatCountrySelection
            // });



            $('#signUp').on('shown.bs.modal', function () {
                if (!$('#country-select-signup').hasClass('select2-hidden-accessible')) {
                    // $('#country-select-signup').select2({
                    //     placeholder: 'Select a country',
                    //     allowClear: true,
                    //     width: '100%',
                    //     templateResult: formatCountry,
                    //     templateSelection: formatCountrySelection
                    // });

                    const flagPath = "{{ asset('website') }}/images/flags/";

                    function formatCountry(state) {
                        if (!state.id) return state.text;

                        const flag = $(state.element).data('flag');
                        const countryName = state.text;

                        return $(`
                        <span>
                            <img src="${flagPath}/${flag}.png" class="img-flag" style="width: 20px; height: 14px; margin-right: 8px;" />
                            ${countryName}
                        </span>
                        `);
                    }

                    // $('#country-select-signup').select2({
                    //     templateResult: formatCountry,
                    //     templateSelection: formatCountry,
                    //     minimumResultsForSearch: -1 // optional: disable search box
                    // });

                    $('#country-select-signup').select2({
    templateResult: formatCountry,
    templateSelection: formatCountry,
    minimumResultsForSearch: -1,
    placeholder: `{{ translate('user_account_setting.select_nationality') }}`,
});

(function(){
  const $select = $('#country-select-signup');

  let buffer = '';
  let bufferTimer = null;
  const BUFFER_CLEAR_MS = 800;

  function clearBuffer(){
    buffer = '';
    if (bufferTimer) { clearTimeout(bufferTimer); bufferTimer = null; }
  }
  function pushToBuffer(ch){
    buffer += ch.toLowerCase();
    if (bufferTimer) clearTimeout(bufferTimer);
    bufferTimer = setTimeout(clearBuffer, BUFFER_CLEAR_MS);
  }

  function highlightInOpen(prefix){
  prefix = prefix.toLowerCase();
  const $results = $('.select2-container--open .select2-results__option');

  if (!$results.length) return;

  let $target = null;
  $results.each(function(){
    const $opt = $(this);

    // skip disabled options
    if ($opt.hasClass('select2-results__option--disabled')) {
      // also remove highlight if Select2 put it here
      $opt.removeClass('select2-results__option--highlighted');
      return;
    }

    const text = $opt.text().trim().toLowerCase();
    if (text.startsWith(prefix)) {
      $target = $opt;
      return false; // break loop
    }
  });

  if ($target && $target.length) {
    $results.removeClass('select2-results__option--highlighted');
    $target.addClass('select2-results__option--highlighted');

    // scroll into view properly
    const $parent = $target.closest('.select2-results__options');
    const top = $target.position().top + $parent.scrollTop();
    $parent.scrollTop(top);
  }
}

$select.on('select2:open', function () {
  // prevent Select2's default typing-to-select
  $('.select2-search__field').prop('disabled', true);

  // remove highlight from disabled immediately on open
  $('.select2-container--open .select2-results__option--disabled')
    .removeClass('select2-results__option--highlighted');

  $(document).off('keydown.customNav').on('keydown.customNav', function(e){
    if (e.altKey || e.ctrlKey || e.metaKey) return;

    const key = e.key;
    if (!key || key.length !== 1) return;
    if (!/[0-9a-zA-Z]/.test(key)) return;

    e.stopImmediatePropagation();
    e.preventDefault();

    pushToBuffer(key);

    // highlight only, don’t select
    highlightInOpen(buffer) || highlightInOpen(key);
  });
});


  $select.on('select2:close', function () {
    $(document).off('keydown.customNav');
    clearBuffer();
  });
})();


                    updateCountryFlag();
                }
            });


            // Function to update flag icon on left side of dropdown (outside select2)
            function updateCountryFlag() {
                var selectedOption = $('#country-select-signup').find('option:selected');
                var flagCode = selectedOption.data('flag');
                var flagElement = $('#selected-flag');

                if (flagCode && selectedOption.val() !== '') {
                    flagElement.html('<span class="flag-icon flag-icon-' + flagCode + '"></span>');
                } else {
                    flagElement.html('');
                }
            }

            // Custom template for dropdown options
            function formatCountry(country) {
                if (!country.id) {
                    return country.text;
                }
                var flag = $(country.element).data('flag');
                if (flag) {
                    var $country = $(
                        '<span><span class="flag-icon flag-icon-' + flag + ' mr-2"></span>' + country.text +
                        '</span>'
                    );
                    return $country;
                }
                return country.text;
            }

            // Custom template for selected item display inside Select2 box
            function formatCountrySelection(country) {
                if (!country.id) {
                    return country.text;
                }
                var flag = $(country.element).data('flag');
                if (flag) {
                    return $('<span><span class="flag-icon flag-icon-' + flag + ' mr-2"></span>' + country.text +
                        '</span>');
                }
                return country.text;
            }

            // Initialize flag display on page load
            // updateCountryFlag();

            // Handle flag badge outside dropdown on selection change
            $('#country-select-signup').on('change', function() {
                updateCountryFlag();
            });




        });
    </script>
    <script>
        $("#pass_btn1").click(function() {
            var passwordInput = $("#createPassword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn1').addClass('fa-eye-slash')
                $('#pass_btn1').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn1').removeClass('fa-eye-slash')
                $('#pass_btn1').addClass('fa-eye')
            }
        });
        $("#pass_btn2").click(function() {
            var passwordInput = $("#confirmPassword");

            if (passwordInput.attr("type") === "password") {
                passwordInput.attr("type", "text");
                $('#pass_btn2').addClass('fa-eye-slash')
                $('#pass_btn2').removeClass('fa-eye')
            } else {
                passwordInput.attr("type", "password");
                $('#pass_btn2').removeClass('fa-eye-slash')
                $('#pass_btn2').addClass('fa-eye')
            }
        });
    </script>
@endpush
