<div class="col-md-12 listing_custom_meta divider listing_data">
    <ul class="list-unstyled d-flex gap-3 m-0 parent-box">
        <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/car-front.svg') }}" alt="" height="20px" width="20px">
            <span> {{ translateDynamic($listing->detail->seats) }} {{ translate('listing_details.seats') }}</span>
        </li>
        @php
            $transmission_types = [
                'manual' => [
                    'icon' => 'manual_transmission.png',
                    'label' => translate('stepper.manual'),
                ],
                'automatic' => [
                    'icon' => 'automatic_transmission.png',
                    'label' => translate('stepper.automatic'),
                ],
                'cvt' => [
                    'icon' => 'cvt_transmission.png',
                    'label' => translate('stepper.cvt'),
                ],
                'dct' => [
                    'icon' => 'dct_transmission.png',
                    'label' => translate('stepper.dct'),
                ],
                'semi_auto' => [
                    'icon' => 'semi_auto_transmission.png',
                    'label' => translate('stepper.semi_automatic'),
                ],
                'no_transmission' => [
                    'icon' => 'not_allowed_icon.png',
                    'label' => translate('stepper.no_transmission'),
                ],
                'other' => [
                    'icon' => 'no_transmission.png',
                    'label' => translate('stepper.other'),
                ],
            ];

            $storedTransmissionType = $listing->detail->transmission;
        @endphp
        @if (isset($transmission_types[$storedTransmissionType]))
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                {{-- <img src="{{ asset('website/images/car-transmission.png') }}" alt="" height="20px" width="20px"> --}}
                <img src="{{ asset('website/images/' . $transmission_types[$storedTransmissionType]['icon']) }}"
                    alt="{{ $transmission_types[$storedTransmissionType]['label'] }}" height="20px" width="25px">
                <span>{{ $transmission_types[$storedTransmissionType]['label'] }}</span>
            </li>
            @endif
            @php
                $engine_types = [
                    'diesel' => [
                        'icon' => 'diesel_engine.png',
                        'label' => translate('stepper.diesel'),
                    ],
                    'electric' => [
                        'icon' => 'electric_engine.png',
                        'label' => translate('stepper.electric'),
                    ],
                    'gasoline' => [
                        'icon' => 'gasoline_engine.png',
                        'label' => translate('stepper.gasoline'),
                    ],
                    'hydrogen' => [
                        'icon' => 'hydrogen_engine.png',
                        'label' => translate('stepper.hydrogen'),
                    ],
                    'hybrid' => [
                        'icon' => 'hybrid_engine.png',
                        'label' => translate('stepper.hybrid'),
                    ],
                    'no_engine' => [
                        'icon' => 'no_engine.png',
                        'label' => translate('stepper.no_engine'),
                    ],
                    'other' => [
                        'icon' => 'other_engine.png',
                        'label' => translate('stepper.other'),
                    ],
                ];

                // Get the stored engine type key
                $storedEngineType = $listing->detail->engine_type;
            @endphp

            @if (isset($engine_types[$storedEngineType]))
                <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                    <img src="{{ asset('website/images/' . $engine_types[$storedEngineType]['icon']) }}"
                        alt="{{ $engine_types[$storedEngineType]['label'] }}" height="20px" width="25px">
                    <span>{{ $engine_types[$storedEngineType]['label'] }}</span>
                </li>
            @endif
            {{-- <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
            <img src="{{ asset('website/images/car-engine.png') }}" alt="" height="20px" width="25px">
            <span>{{ $listing->detail->engine_type ?? '-' }}</span>
        </li> --}}
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px">
                @if ($listing->detail->pet == 'yes')
                    {{ translate('listing_details.pets_allowed') }}
                @else
                    {{ translate('listing_details.pets_not_allowed') }}
                @endif
            </li>
            <li class="catg box d-flex gap-2 align-items-center" data-aos="fade">
                {{-- <img src="{{ asset('website/images/dog-icon.png') }}" alt="" height="20px" width="20px"> --}}
                @if ($listing->detail->chauffeur == 'yes')
                    <img src="{{ asset('website/images/driver.png') }}" alt="" height="20px" width="20px">
                    {{ translate('listing_details.chauffeur_included') }}
                @else
                    <img src="{{ asset('website/images/driver.png') }}" alt="" height="20px" width="20px">
                    {{ translate('listing_details.chauffeur_not_included') }}
                @endif
            </li>
    </ul>
</div>
{{-- Key and feature --}}
@if (isset($listing->key_features[0]))
    <div class="col-lg-12 divider listing_data listing_key_feature">
        <div class="key_features">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.key_features') }}</h3>
            <div class="parent-feature parent-box row g-0 gap-3 align-items-start">
                @foreach ($listing->key_features as $key_feature)
                    <div class="box col-md-3" data-aos="fade">
                        <h6 class="fs-16 semi-bold">{{ translateDynamic($key_feature->title) }}</h6>
                        <p class="fs-12">{!! translateDynamic($key_feature->description) !!}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
@endif
{{-- End Key and feature --}}
{{-- Include and not include for Car --}}
@if (isset($listing->includes[0]))
    <div class="col-lg-12 divider listing_data listing_include">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.whats_included') }}</h3>
            <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                @forelse ($listing->includes as $include)
                    <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                        <img src="{{ asset('website/images/square-check.svg') }}" height="20px" width="20px"
                            alt="">
                        <span>{{ translateDynamic($include->name) }}</span>
                    </div>
                @empty
                    <p>{{ translate('listing_details.no_include_added') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endif
@if (isset($listing->not_includes[0]))
    <div class="col-lg-12 divider listing_data listing_not_include">
        <div class="amenities-box">
            <h3 class="fs-22 listing_data_heading">{{ translate('listing_details.not_included') }}</h3>
            <div class="parent-box d-flex flex-wrap gap-3 align-items-center">
                @forelse ($listing->not_includes as $not_include)
                    <div class="box d-flex gap-2 align-items-center" data-aos="fade">
                        <img src="{{ asset('website/images/ticksquare.svg') }}" alt="" height="20px"
                            width="20px">
                        <span>{{ translateDynamic($not_include->name) }}</span>
                    </div>
                @empty
                    <p>{{ translate('listing_details.no_not_include_added') }}</p>
                @endforelse
            </div>
        </div>
    </div>
@endif
{{-- End Include and not include Car --}}
